"use server";

import { dbClient } from '@/database/client';
import { Airports } from '@prisma/client';

export async function getOriginsAndDestination() {
  try {
    const result = await dbClient.airports.findMany();
    const originsSet = new Set<string>(result.map((item: Airports) => item.iataCode));
    const destinationsSet = new Set<string>(result.map((item: Airports) => item.iataCode));
    return {
      origins: Array.from(originsSet).sort(),
      destinations: Array.from(destinationsSet).sort(),
    };
  } catch (error) {
    console.error('Error fetching origins and destinations:', error);
    throw error;
  }
}
