"use server";

import { AERO_API_KEY, AERO_API_URL } from '@/constants/aero.constant';
import { AeroFlightItem, AeroSearchResponse } from '@/interfaces/aero-search.response';

export async function getFlight(originAirport: string, destinationAirport: string) {
  const currentDate = new Date();
  const nextYearDate = new Date(currentDate);
  nextYearDate.setFullYear(nextYearDate.getFullYear() + 1);
  const startDate = currentDate.toISOString().split('T')[0];
  const endDate = nextYearDate.toISOString().split('T')[0];
  try {
      const result = await fetch(
        `${AERO_API_URL}/search?origin_airport=${originAirport}&destination_airport=${destinationAirport}&start_date=${startDate}&end_date=${endDate}&take=5000`,
      {
        headers: {
          'Partner-Authorization': AERO_API_KEY,
          accept: 'application/json',
        },
        cache: 'no-store',
      }
    );
    const data = (await result.json()) as AeroSearchResponse;
    return data;
  } catch (error) {
    console.error('Error fetching origins and destinations:', error);
    throw error;
  }
}
