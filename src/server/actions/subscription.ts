'use server';

import { authOptions } from '@/configs/auth';
import { dbClient } from '@/database/client';
import { getServerSession } from 'next-auth';
import { revalidatePath } from 'next/cache';
import { SubscriptionPlan } from '@/enums/subscription-plan.enum';
import { AppSession } from '@/types/session.type';

interface SubscriptionData {
  subscriptionPlan: SubscriptionPlan;
  searchLimit: number;
  searchesUsed: number;
  remainingSearches: number;
  lastResetDate: Date;
}

// Get subscription info for the current tenant
export async function getSubscription(): Promise<SubscriptionData | null> {
  try {
    const session = await getServerSession(authOptions);

    const userId = (session as AppSession)?.user?.id;

    if (!userId) {
      throw new Error('Unauthorized');
    }

    let user = await dbClient.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Check if we need to reset the monthly counter
    const now = new Date();
    const lastReset = new Date(user.lastResetDate);

    // Reset if it's a new month
    if (now.getMonth() !== lastReset.getMonth() || now.getFullYear() !== lastReset.getFullYear()) {
      user = await dbClient.user.update({
        where: { id: user.id },
        data: {
          searchesUsed: 0,
          lastResetDate: now,
        },
      });
    }

    const remainingSearches =
      user.subscriptionPlan === SubscriptionPlan.Unlimited
        ? -1 // Unlimited
        : Math.max(0, user.searchLimit - user.searchesUsed);

    return {
      subscriptionPlan: user.subscriptionPlan as SubscriptionPlan,
      searchLimit: user.searchLimit,
      searchesUsed: user.searchesUsed,
      remainingSearches,
      lastResetDate: user.lastResetDate,
    };
  } catch (error) {
    console.error('Error fetching subscription:', error);
    return null;
  }
}

// Consume one search call
export async function consumeSearch(): Promise<{
  success: boolean;
  error?: string;
  subscriptionData?: SubscriptionData;
}> {
  try {
    const session = await getServerSession(authOptions);
    const userId = (session as AppSession)?.user?.id;
    if (!userId) {
      return { success: false, error: 'Unauthorized' };
    }

    let user = await dbClient.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return { success: false, error: 'User not found' };
    }

    // Check if we need to reset the monthly counter
    const now = new Date();
    const lastReset = new Date(user.lastResetDate);

    if (now.getMonth() !== lastReset.getMonth() || now.getFullYear() !== lastReset.getFullYear()) {
      user = await dbClient.user.update({
        where: { id: user.id },
        data: {
          searchesUsed: 0,
          lastResetDate: now,
        },
      });
    }

    // Check if user has remaining searches (unlimited plan has no limit)
    if (user.subscriptionPlan !== SubscriptionPlan.Unlimited && user.searchesUsed >= user.searchLimit) {
      return {
        success: false,
        error: 'Search limit exceeded',
      };
    }

    // Increment search count for standard plan
    if (user.subscriptionPlan !== SubscriptionPlan.Unlimited) {
      user = await dbClient.user.update({
        where: { id: user.id },
        data: {
          searchesUsed: user.searchesUsed + 1,
        },
      });
    }

    const remainingSearches =
      user.subscriptionPlan === SubscriptionPlan.Unlimited
        ? -1
        : Math.max(0, user.searchLimit - user.searchesUsed);

    const subscriptionData: SubscriptionData = {
      subscriptionPlan: user.subscriptionPlan as SubscriptionPlan,
      searchLimit: user.searchLimit,
      searchesUsed: user.searchesUsed,
      remainingSearches,
      lastResetDate: user.lastResetDate,
    };

    revalidatePath('/');

    return {
      success: true,
      subscriptionData,
    };
  } catch (error) {
    console.error('Error consuming search:', error);
    return { success: false, error: 'Internal server error' };
  }
}
