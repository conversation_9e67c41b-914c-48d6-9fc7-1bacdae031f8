'use server';

import { dbClient } from '@/database/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/configs/auth';
import { AppSession } from '@/types/session.type';

export interface SearchHistoryData {
  originAirport: string;
  destinationAirport: string;
  airlines: string[];
  source: string[];
}

export async function saveSearchHistory(searchData: SearchHistoryData) {
  try {
    const session = await getServerSession(authOptions);

    const userId = (session as AppSession).user?.id;
    if (!userId) {
      throw new Error('User not authenticated');
    }

    // // Check if this exact search already exists for this user
    // const existingSearch = await dbClient.searchHistories.findFirst({
    //   where: {
    //     userId: userId,
    //     originAirport: searchData.originAirport,
    //     destinationAirport: searchData.destinationAirport,
    //     airlines: {
    //       equals: searchData.airlines,
    //     },
    //     source: {
    //       equals: searchData.source,
    //     },
    //   },
    // });

    // if (existingSearch) {
    //   // Update the existing search timestamp
    //   await dbClient.searchHistories.update({
    //     where: {
    //       id: existingSearch.id,
    //     },
    //     data: {
    //       updatedAt: new Date(),
    //     },
    //   });
    //   return existingSearch;
    // } else {
    //   // Create new search history
    //   const newSearchHistory = await dbClient.searchHistories.create({
    //     data: {
    //       userId: userId,
    //       originAirport: searchData.originAirport,
    //       destinationAirport: searchData.destinationAirport,
    //       airlines: searchData.airlines,
    //       source: searchData.source,
    //     },
    //   });
    //   return newSearchHistory;
    // }
    // Create new search history
    const newSearchHistory = await dbClient.searchHistories.create({
      data: {
        userId: userId,
        originAirport: searchData.originAirport,
        destinationAirport: searchData.destinationAirport,
        airlines: searchData.airlines,
        source: searchData.source,
      },
    });
    return newSearchHistory;
  } catch (error) {
    console.error('Failed to save search history:', error);
    throw new Error('Failed to save search history');
  }
}

export async function getSearchHistory(limit: number = 10) {
  try {
    const session = await getServerSession(authOptions);

    const userId = (session as AppSession).user?.id;
    if (!userId) {
      throw new Error('User not authenticated');
    }

    const searchHistories = await dbClient.searchHistories.findMany({
      where: {
        userId: userId,
      },
      orderBy: {
        updatedAt: 'desc',
      },
      take: limit,
    });

    return searchHistories;
  } catch (error) {
    console.error('Failed to get search history:', error);
    throw new Error('Failed to get search history');
  }
}

export async function getLatestSearchForRoute(originAirport: string, destinationAirport: string) {
  try {
    const session = await getServerSession(authOptions);

    const userId = (session as AppSession).user?.id;
    if (!userId) {
      return null;
    }

    const latestSearch = await dbClient.searchHistories.findFirst({
      where: {
        userId: userId,
        originAirport,
        destinationAirport,
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    return latestSearch;
  } catch (error) {
    console.error('Failed to get latest search for route:', error);
    return null;
  }
}

export async function deleteSearchHistory(searchId: string) {
  try {
    const session = await getServerSession(authOptions);

    const userId = (session as AppSession).user?.id;
    if (!userId) {
      throw new Error('User not authenticated');
    }

    // Verify the search history belongs to the current user
    const searchHistory = await dbClient.searchHistories.findFirst({
      where: {
        id: searchId,
        userId: userId,
      },
    });

    if (!searchHistory) {
      throw new Error('Search history not found or unauthorized');
    }

    await dbClient.searchHistories.delete({
      where: {
        id: searchId,
      },
    });

    return { success: true };
  } catch (error) {
    console.error('Failed to delete search history:', error);
    throw new Error('Failed to delete search history');
  }
}
