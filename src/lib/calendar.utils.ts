import { format } from 'date-fns';

export interface MonthData {
  month: number;
  year: number;
  display: string;
}

/**
 * Generates an array of month data between two dates
 * @param startDateStr - Start date in string format
 * @param endDateStr - End date in string format
 * @returns Array of month data objects
 */
export const generateMonthsInRange = (startDateStr: string, endDateStr: string): MonthData[] => {
  const months: MonthData[] = [];
  const currentDate = new Date(startDateStr);
  const endDate = new Date(endDateStr);
  
  while (currentDate.getTime() <= endDate.getTime()) {
    months.push({
      month: currentDate.getMonth() + 1, // Convert from 0-11 to 1-12
      year: currentDate.getFullYear(),
      display: format(currentDate, 'MMMM yyyy'),
    });
    currentDate.setMonth(currentDate.getMonth() + 1);
  }
  
  return months;
};

/**
 * Splits an array into chunks of specified size
 * @param array - Array to split
 * @param chunkSize - Size of each chunk
 * @returns Array of chunks
 */
export const chunkArray = <T>(array: T[], chunkSize: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
};
