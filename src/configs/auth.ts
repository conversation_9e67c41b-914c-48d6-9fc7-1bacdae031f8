import { AuthOptions } from 'next-auth';
import EmailProvider from 'next-auth/providers/email';
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import { dbClient } from '@/database/client';

// Custom adapter that prevents creating new users
const customAdapter = {
  ...PrismaAdapter(dbClient),
  // Override createUser to prevent new user creation
  createUser: async (user: any) => {
    throw new Error('User registration is disabled. Only existing users can sign in.');
  },
};

export const authOptions: AuthOptions = {
  adapter: customAdapter,
  providers: [
    EmailProvider({
      server: {
        host: process.env.EMAIL_SERVER_HOST,
        port: process.env.EMAIL_SERVER_PORT,
        auth: {
          user: process.env.EMAIL_SERVER_USER,
          pass: process.env.EMAIL_SERVER_PASSWORD,
        },
      },
      from: process.env.EMAIL_FROM,
      maxAge: 10 * 60, // Magic link expires in 10 minutes (600 seconds)
      // async sendVerificationRequest({ identifier, url, provider }) {
      //   console.log("Sending magic link to:", identifier);
      //   console.log("Magic link URL:", url);
      //   // Default email sending logic (handled by NextAuth)
      //   const { host } = new URL(url);
      //   const transport = require("nodemailer").createTransport(provider.server);
      //   await transport.sendMail({
      //     to: identifier,
      //     from: provider.from,
      //     subject: `Login to ${host}`,
      //     text: `Click the link to login: ${url}`,
      //     html: `<p>Click the <a href="${url}">link</a> to login.</p>`,
      //   });
      // },
    }),
  ],
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: "database", // Use database for session storage (required with Prisma Adapter)
    maxAge: 365 * 24 * 60 * 60, // Session expires in 365 days
  },
  pages: {
    signIn: "/signin", // Custom sign-in page
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      // Check if user exists in database before allowing sign-in
      if (user.email) {
        try {
          const existingUser = await dbClient.user.findUnique({
            where: { email: user.email },
          });
          
          if (!existingUser) {
            console.log(`Sign-in attempt rejected for non-existing user: ${user.email}`);
            return false; // Prevent sign-in for non-existing users
          }

          // Update lastLoginAt when existing user signs in
          if (existingUser.id) {
            await dbClient.user.update({
              where: { id: existingUser.id },
              data: { lastLoginAt: new Date() },
            });
          }
          
          return true; // Allow sign-in for existing users
        } catch (error) {
          console.error('Error checking user existence:', error);
          return false; // Prevent sign-in on database errors
        }
      }
      
      return false; // Prevent sign-in if no email provided
    },
    async session({ session, user }: { session: any, user: any }) {
      session.user.id = user.id; // Add user ID to session
      return session;
    },
  },
};
