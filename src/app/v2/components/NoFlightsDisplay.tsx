'use client';

import React from 'react';
import Image from 'next/image';

interface NoFlightsDisplayProps {
  message?: string;
  iconSize?: number;
}

export const NoFlightsDisplay: React.FC<NoFlightsDisplayProps> = ({ 
  message = 'No available seat',
  iconSize = 125
}) => {
  return (
    <div className="flex flex-col justify-center items-center gap-3 py-10 px-8">
      <div className="opacity-50">
        <Image
          src="/images/no-available-flight-calendar.svg"
          alt="No available flights"
          width={iconSize}
          height={iconSize}
          priority={false}
        />
      </div>
      <p className="text-base font-normal text-gray-600">{message}</p>
    </div>
  );
};
