'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { FlightClassData, FlightClassSection } from './FlightClassSection';

export interface FlightResultsData {
  economy: FlightClassData;
  business: FlightClassData;
  premium: FlightClassData;
  first: FlightClassData;
}

interface FlightResultsProps {
  data: FlightResultsData;
}

export const FlightResults: React.FC<FlightResultsProps> = ({ data }) => {
  return (
    <Card className="bg-white border border-gray-200 rounded-xl shadow-sm">
      <CardContent className="p-0">
        <div className="flex flex-row">
          <FlightClassSection className="Economy" data={data.economy} />
          <FlightClassSection className="Business" data={data.business} />
          <FlightClassSection className="Premium" data={data.premium} />
          <FlightClassSection className="First" data={data.first} />
        </div>
      </CardContent>
    </Card>
  );
};
