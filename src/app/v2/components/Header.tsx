'use client';

import React from 'react';
import SignOutButton from '@/components/auth/SignOutButton';
import Link from 'next/link';
import { Session } from 'next-auth';
import Image from 'next/image';
import { IoPersonOutline } from 'react-icons/io5';
import { TbExternalLink } from 'react-icons/tb';
import { IoIosLogOut } from "react-icons/io";
interface HeaderProps {
  session: Session;
}

export const Header: React.FC<HeaderProps> = ({ session }) => {
  return (
    <header className="relative max-w-7xl h-60 mx-auto px-6 flex justify-between items-center lg:px-12 py-6">
      {/* World Map Background */}
      <div
        className="absolute top-0 left-1/2 transform -translate-x-1/2 w-full h-full opacity-50 overflow-hidden pointer-events-none bg-center bg-cover bg-no-repeat"
        style={{
          backgroundImage: "url('/images/world-map.svg')",
          backgroundSize: 'contain',
          aspectRatio: '2/1',
        }}
      />

      {/* Logo */}
      <div className="flex items-center space-x-2">
        <Link href="/v2" className="flex items-center">
          <Image src="/images/logo.svg" alt="Logo" width={188} height={48} />
        </Link>
      </div>


      {/* User Profile Section */}
      <div className="flex items-center space-x-3 relative z-10">
        {session && session.user ? (
          <div className="flex items-center space-x-3">
            {/* User Avatar */}
            <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
              <IoPersonOutline className="w-5 h-5 text-[#2259C8]" />
            </div>
            
            {/* User Email */}
            <span className="text-white font-medium text-sm">
              {session.user.email}
            </span>
            
            {/* External Link Icon */}
            <button 
              className="text-white hover:text-white/80 transition-colors"
              onClick={() => window.open('#', '_blank')}
              aria-label="Open external link"
            >
              <IoIosLogOut className="w-6 h-6" />
            </button>
          </div>
        ) : (
          <Link 
            href="/signin" 
            className="text-white hover:text-white/80 transition-colors font-medium"
          >
            Sign In
          </Link>
        )}
      </div>
    </header>
  );
};
