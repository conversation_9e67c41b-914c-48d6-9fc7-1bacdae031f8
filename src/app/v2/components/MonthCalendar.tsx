'use client';

import React, { useMemo, useCallback } from 'react';
import { TicketAvailabilityCell } from './TicketAvailabilityCell';
import { 
  startOfMonth, 
  endOfMonth, 
  startOfWeek, 
  endOfWeek, 
  addDays, 
  format, 
  isSameMonth, 
  getDate,
} from 'date-fns';

export interface DayData {
  /** Date in ISO format (YYYY-MM-DD) */
  date: string;
  /** Number of available seats for this date */
  seatCount: number;
}

interface MonthCalendarProps {
  /** Display name of the month (e.g., "January 2024") */
  monthName: string;
  /** Month number (1-12) */
  month: number;
  /** Year (e.g., 2024) */
  year: number;
  /** Array of day data for this month */
  days: DayData[];
}

const WEEKDAYS = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

export const MonthCalendar: React.FC<MonthCalendarProps> = ({ month, year, monthName, days }) => {
  // Memoize the current month date to prevent unnecessary recalculations
  const currentMonth = useMemo(() => new Date(year, month - 1, 1), [year, month]);
  
  // Memoize calendar boundaries
  const calendarBounds = useMemo(() => {
    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(currentMonth);
    
    return {
      start: startOfWeek(monthStart, { weekStartsOn: 1 }),
      end: endOfWeek(monthEnd, { weekStartsOn: 1 }),
    };
  }, [currentMonth]);

  // Memoize calendar days generation
  const calendarDays = useMemo(() => {
    const calendarDaysList: Date[] = [];
    let currentDay = calendarBounds.start;

    while (currentDay <= calendarBounds.end) {
      calendarDaysList.push(new Date(currentDay));
      currentDay = addDays(currentDay, 1);
    }

    return calendarDaysList;
  }, [calendarBounds]);

  // Memoize weeks grouping
  const weeks = useMemo(() => {
    const weeksList: Date[][] = [];
    for (let i = 0; i < calendarDays.length; i += 7) {
      weeksList.push(calendarDays.slice(i, i + 7));
    }
    return weeksList;
  }, [calendarDays]);

  // Memoize seat count lookup for better performance
  const seatCountMap = useMemo(() => {
    const map = new Map<string, number>();
    days.forEach((day) => {
      map.set(day.date, day.seatCount);
    });
    return map;
  }, [days]);

  const getSeatCount = useCallback((date: Date): number => {
    const dateString = format(date, 'yyyy-MM-dd');
    return seatCountMap.get(dateString) || 0;
  }, [seatCountMap]);

  return (
    <div className="flex flex-col justify-center items-center gap-3 p-3 w-full border-r border-gray-200 last:border-r-0">
      {/* Month header */}
      <div className="w-full">
        <h3 className="text-sm font-normal text-gray-700 text-center">{monthName}</h3>
      </div>

      {/* Calendar grid */}
      <div className="flex flex-col justify-center items-center w-full gap-1">
        {/* Weekday headers */}
        <div className="grid grid-cols-7 gap-1 w-full mb-2">
          {WEEKDAYS.map((day) => (
            <div key={day} className="text-xs font-medium text-gray-500 text-center py-1">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar weeks */}
        <div className="grid grid-cols-7 gap-1 w-full">
          {weeks.map((week, weekIndex) =>
            week.map((date, dayIndex) => {
              const isCurrentMonth = isSameMonth(date, currentMonth);
              const dayNumber = getDate(date);
              const seatCount = getSeatCount(date);
              const cellKey = `${weekIndex}-${dayIndex}`;

              if (!isCurrentMonth) {
                return (
                  <div 
                    key={cellKey} 
                    className="w-full h-8 bg-gray-50 opacity-30 rounded-md"
                    aria-hidden="true"
                  />
                );
              }

              return (
                <div key={cellKey} className="w-full">
                  <TicketAvailabilityCell
                    date={dayNumber}
                    seatCount={seatCount}
                  />
                </div>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};