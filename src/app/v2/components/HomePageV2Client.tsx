'use client';

import React, { useEffect, useState } from 'react';
import { Header } from '@/app/v2/components/Header';
import { SearchSection } from '@/app/v2/components/SearchSection';
import { ResultSection } from '@/app/v2/components/ResultSection';
import { Session } from 'next-auth';
import { Footer } from '@/app/v2/components/Footer';
import { useSubscription } from '@/hooks/useSubscription';
import { useFlightSearch } from '@/hooks/useFlightSearch';
import { FilterState } from '@/types/filters.types';
import { useFlightSearchV2 } from '@/hooks/useFlightSearchV2';

interface HomePageV2ClientProps {
  origins: string[];
  destinations: string[];
  session: Session;
}

export const HomePageV2Client: React.FC<HomePageV2ClientProps> = ({ origins, destinations, session }) => {
  const [originAirport, setOriginAirport] = useState('');
  const [destinationAirport, setDestinationAirport] = useState('');
  const [isDirectFiltered, setIsDirectFiltered] = useState(false);
  const {
    filteredFlightData,
    sources,
    airlines,
    isLoadingFilteredData,
    isLoadingFlightData,

    fetchFlightData,
    handleFilterResult,
  } = useFlightSearchV2();

  const { subscriptionData, consumeSubscription } = useSubscription();

  const handleFilterChanged = (filterState: FilterState) => {
    if (filterState.originAirport !== originAirport) {
      setOriginAirport(filterState.originAirport);
    }
    if (filterState.destinationAirport !== destinationAirport) {
      setDestinationAirport(filterState.destinationAirport);
    }
    if (filterState.isDirect !== isDirectFiltered) {
      setIsDirectFiltered(filterState.isDirect);
    }
  };

  useEffect(() => {
    if (originAirport && destinationAirport) {
      (async () => {
        await fetchFlightData(originAirport, destinationAirport);
      })();
    }
  }, [originAirport, destinationAirport]);

  const handleSearch = async (filterState: FilterState) => {
    // Apply filters and generate results (this will trigger the useEffect above)
    handleFilterResult(filterState);
    consumeSubscription();
  };

  return (
    <div className=" min-h-screen bg-gray-100 font-sans">
      {/* Banner Section with Logo */}
      <div className="relative aspect-[4.8/1] bg-gradient-to-b from-[#2259C8] to-[#1344A7] ">
        {/* Header */}
        <Header session={session} />
      </div>
      {/* Main Container */}
      <div className="relative z-100 max-w-7xl mx-auto px-6 -mt-40">
        <div className="space-y-3">
          {/* Search Section */}
          {subscriptionData && (
            <SearchSection
              origins={origins}
              destinations={destinations}
              onSearchResults={handleSearch}
              onFilterChanged={handleFilterChanged}
              airlines={airlines}
              sources={sources}
              subscriptionPlan={subscriptionData.subscriptionPlan}
              searchLimit={subscriptionData.searchLimit}
              searchesUsed={subscriptionData.searchesUsed}
              isLoadingFlightData={isLoadingFlightData}
            />
          )}

          {/* Result Section */}
          <ResultSection flightData={filteredFlightData} isDirectFiltered={isDirectFiltered} />
        </div>
      </div>
      {/* Footer Section */}
      <Footer />
    </div>
  );
};
