'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { SearchOutlined } from '@ant-design/icons';
import { useFlightSearch } from '@/hooks/useFlightSearch';
import { FilterState } from '@/types/filters.types';
import { FlightClass } from '@/enums/flight-class.enum';
import { DATA_FRESHNESS_OPTIONS } from '@/constants/airlines.constant';
import { Select, Spin, Button } from 'antd';
import { useSubscription } from '@/hooks/useSubscription';
import { SubscriptionPlan } from '@/enums/subscription-plan.enum';

const { Option } = Select;

interface SearchSectionProps {
  origins: string[];
  destinations: string[];
  airlines: string[];
  sources: string[];
  subscriptionPlan: SubscriptionPlan;
  searchLimit: number;
  searchesUsed: number;
  isLoadingFlightData: boolean;

  onSearchResults: (filterState: FilterState) => void;
  onFilterChanged: (filterState: FilterState) => void;
}

export const SearchSection: React.FC<SearchSectionProps> = ({
  origins,
  destinations,
  airlines,
  sources,
  subscriptionPlan,
  searchLimit,
  searchesUsed,
  isLoadingFlightData,

  onSearchResults,
  onFilterChanged,
}) => {
  // Form state
  const [formData, setFormData] = useState({
    originAirport: '',
    destinationAirport: '',
    airline: '',
    frequentFlyerProgram: '',
    dataFreshness: '',
    redemptionPointMin: 0,
    redemptionPointMax: 55000,
    directFlightOnly: false,
  });

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear dependent fields when origin or destination changes
    if (field === 'originAirport' || field === 'destinationAirport') {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
        airline: '',
        frequentFlyerProgram: '',
      }));
    }
  };

  const handleSearch = () => {
    onSearchResults({
      originAirport: formData.originAirport,
      destinationAirport: formData.destinationAirport,
      selectedAirlines: [formData.airline],
      selectedSource: [formData.frequentFlyerProgram],
      isDirect: formData.directFlightOnly,
      maxFilterPoints: formData.redemptionPointMax,
      minFilterPoints: formData.redemptionPointMin,
    });
  };

  useEffect(() => {
    onFilterChanged({
      originAirport: formData.originAirport,
      destinationAirport: formData.destinationAirport,
      selectedAirlines: [formData.airline],
      selectedSource: [formData.frequentFlyerProgram],
      isDirect: formData.directFlightOnly,
      maxFilterPoints: formData.redemptionPointMax,
      minFilterPoints: formData.redemptionPointMin,
    });
  }, [
    formData.originAirport,
    formData.destinationAirport,
    formData.airline,
    formData.frequentFlyerProgram,
    formData.directFlightOnly,
    formData.redemptionPointMax,
    formData.redemptionPointMin,
  ]);

  const isFormValid =
    formData.originAirport &&
    formData.destinationAirport &&
    formData.airline &&
    formData.frequentFlyerProgram.length > 0;

  const isDisabledSelectAirline =
    !formData.originAirport || !formData.destinationAirport || isLoadingFlightData;

  return (
    <section className="w-full px-6">
      <div className="max-w-7xl mx-auto">
        <Card className="bg-white border border-gray-200 rounded-xl shadow-sm">
          <CardContent className="p-5">
            {/* Header Row */}
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center gap-2">
                {/* Featured Search Icon */}
                <div className="relative w-12 h-12">
                  <div className="absolute inset-0 bg-[#155EEF] rounded-full opacity-10"></div>
                  <div className="absolute inset-2 bg-[#155EEF]  rounded-full flex items-center justify-center">
                    <SearchOutlined className="text-white text-base" />
                  </div>
                </div>
                <h2 className="text-lg font-medium text-gray-900">Search Award Ticket</h2>
              </div>

              {/* Credit Counter */}
              <div className="flex items-center gap-2 bg-white border border-gray-300 rounded-lg px-3 py-2 shadow-sm">
                <div className="text-orange-500">⚡</div>
                {subscriptionPlan === SubscriptionPlan.Unlimited ? (
                  <span className="text-sm font-semibold text-gray-900">Unlimited</span>
                ) : (
                  <div className="flex items-center gap-1">
                    <span className="text-sm font-semibold text-gray-900">{searchesUsed || 0}</span>
                    <span className="text-sm text-gray-600">/</span>
                    <span className="text-sm text-gray-600">{searchLimit || 0}</span>
                  </div>
                )}

                <span className="text-xs text-gray-600">credit remaining</span>
              </div>
            </div>

            {/* Search Form */}
            <div className="space-y-6">
              {/* Main Selection Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-3">
                {/* Origin Airports */}
                <div className="space-y-1.5">
                  <label className="block text-sm font-medium text-gray-700">
                    Origin Airports <span className="text-red-500">*</span>
                  </label>
                  <Select
                    showSearch
                    placeholder="Select origin airports"
                    value={formData.originAirport}
                    onChange={(value) => handleInputChange('originAirport', value)}
                    className="w-full"
                    size="large"
                  >
                    {origins.map((origin) => (
                      <Option key={origin} value={origin}>
                        {origin}
                      </Option>
                    ))}
                  </Select>
                </div>

                {/* Destination Airport */}
                <div className="space-y-1.5">
                  <label className="block text-sm font-medium text-gray-700">
                    Destination Airport <span className="text-red-500">*</span>
                  </label>
                  <Select
                    showSearch
                    placeholder="Select destination airports"
                    value={formData.destinationAirport}
                    onChange={(value) => handleInputChange('destinationAirport', value)}
                    className="w-full"
                    size="large"
                  >
                    {destinations
                      .filter((dest) => dest !== formData.originAirport)
                      .map((destination) => (
                        <Option key={destination} value={destination}>
                          {destination}
                        </Option>
                      ))}
                  </Select>
                </div>

                {/* Airline */}
                <div className="space-y-1.5">
                  <label className="block text-sm font-medium text-gray-700">
                    Airline <span className="text-red-500">*</span>
                  </label>
                  <Select
                    showSearch
                    placeholder={isLoadingFlightData ? 'Loading airlines...' : 'Select airline'}
                    value={formData.airline}
                    onChange={(value) => handleInputChange('airline', value)}
                    className="w-full"
                    size="large"
                    disabled={isDisabledSelectAirline}
                    notFoundContent={isLoadingFlightData ? <Spin size="small" /> : 'No airlines found'}
                  >
                    {airlines.map((airline) => (
                      <Option key={airline} value={airline}>
                        {airline}
                      </Option>
                    ))}
                  </Select>
                </div>

                {/* Frequent Flyer Program */}
                <div className="space-y-1.5">
                  <label className="block text-sm font-medium text-gray-700">
                    Frequent Flyer Program <span className="text-red-500">*</span>
                  </label>
                  <Select
                    showSearch
                    placeholder={isLoadingFlightData ? 'Loading programs...' : 'Select program'}
                    value={formData.frequentFlyerProgram}
                    onChange={(value) => handleInputChange('frequentFlyerProgram', value)}
                    className="w-full"
                    size="large"
                    disabled={isDisabledSelectAirline}
                    notFoundContent={isLoadingFlightData ? <Spin size="small" /> : 'No programs found'}
                  >
                    {sources.map((program) => (
                      <Option key={program} value={program}>
                        {program}
                      </Option>
                    ))}
                  </Select>
                </div>

                {/* Data Freshness */}
                <div className="space-y-1.5">
                  <label className="block text-sm font-medium text-gray-700">Data Freshness</label>
                  <Select
                    placeholder="Select type"
                    value={formData.dataFreshness}
                    onChange={(value) => handleInputChange('dataFreshness', value)}
                    className="w-full"
                    size="large"
                  >
                    {DATA_FRESHNESS_OPTIONS.map((option) => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </div>
              </div>

              {/* Filter Row */}
              <div className="flex justify-between items-center border-t border-gray-200 pt-6">
                <div className="flex items-center gap-6">
                  {/* Redemption Point Range */}
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-700">Redemption Point</span>
                    <div className="flex items-center gap-2">
                      <input
                        type="number"
                        value={formData.redemptionPointMin}
                        onChange={(e) =>
                          handleInputChange('redemptionPointMin', parseInt(e.target.value) || 0)
                        }
                        className="w-20 px-3 py-2 border border-gray-300 text-black rounded-lg text-center"
                        min="0"
                      />
                      <span className="text-gray-500">-</span>
                      <input
                        type="number"
                        value={formData.redemptionPointMax}
                        onChange={(e) =>
                          handleInputChange('redemptionPointMax', parseInt(e.target.value) || 0)
                        }
                        className="w-24 px-3 py-2 border border-gray-300 text-black rounded-lg text-center"
                        min="0"
                      />
                    </div>
                  </div>

                  {/* Vertical Divider */}
                  <div className="h-4 w-px bg-gray-300"></div>

                  {/* Direct Flight Only Checkbox */}
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.directFlightOnly}
                      onChange={(e) => handleInputChange('directFlightOnly', e.target.checked)}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-700">Direct Flight Only</span>
                  </label>
                </div>

                {/* Search Button */}
                <Button
                  type="primary"
                  onClick={() => handleSearch()}
                  disabled={!isFormValid}
                  size="large"
                  className="bg-blue-600 hover:bg-blue-700 border-0 font-semibold px-6 h-10 rounded-lg shadow-sm"
                >
                  Search
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};
