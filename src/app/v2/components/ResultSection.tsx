'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import Image from 'next/image';
import { FlightResults, FlightResultsData } from './FlightResults';
import { CalendarGrid } from './CalendarGrid';
import { addYears } from 'date-fns';
import { AeroFlightItem } from '@/interfaces/aero-search.response';
import { FilterState } from '@/types/filters.types';
import { FLIGHT_CLASS_CONFIG } from '@/constants/flight-class.constant';
import { DayData } from './MonthCalendar';
import { FilteredFlightData } from '@/types/filters-v2.types';

interface ResultSectionProps {
  flightData: FilteredFlightData | null;
  isDirectFiltered: boolean;
}

export const ResultSection: React.FC<ResultSectionProps> = ({ flightData, isDirectFiltered }) => {
  const [flightClassData, setFlightClassData] = useState<FlightResultsData | null>(null);

  useEffect(() => {
    if (flightData) {
      setFlightClassData({
        economy: {
          isDirectFlight: isDirectFiltered,
          pointsRequired: flightData.Economy.summary.pointsRequired,
          availableSeats: flightData.Economy.summary.availableSeats,
        },
        business: {
          isDirectFlight: isDirectFiltered,
          pointsRequired: flightData.Business.summary.pointsRequired,
          availableSeats: flightData.Business.summary.availableSeats,
        },
        premium: {
          isDirectFlight: isDirectFiltered,
          pointsRequired: flightData.Premium.summary.pointsRequired,
          availableSeats: flightData.Premium.summary.availableSeats,
        },
        first: {
          isDirectFlight: isDirectFiltered,
          pointsRequired: flightData.First.summary.pointsRequired,
          availableSeats: flightData.First.summary.availableSeats,
        },
      });
    }
  }, [flightData]);

  if (flightData && flightClassData) {
    return (
      <section className="w-full px-6 mt-3">
        <div className="max-w-7xl mx-auto space-y-4">
          {/* Flight Class Results */}
          <FlightResults data={flightClassData} />
          {/* Calendar Grid */}
          <CalendarGrid
            title="Economy"
            onExport={() => {}}
            days={flightData.Economy.flightByDate}
            hasFlights={flightData.Economy.summary.availableSeats > 0}
          />
          <CalendarGrid
            title="Premium"
            onExport={() => {}}
            days={flightData.Premium.flightByDate}
            hasFlights={flightData.Premium.summary.availableSeats > 0}
          />
          <CalendarGrid
            title="Business"
            onExport={() => {}}
            days={flightData.Business.flightByDate}
            hasFlights={flightData.Business.summary.availableSeats > 0}
          />
          <CalendarGrid
            title="First"
            onExport={() => {}}
            days={flightData.First.flightByDate}
            hasFlights={flightData.First.summary.availableSeats > 0}
          />
        </div>
      </section>
    );
  }

  return (
    <section className="w-full px-6 mt-3">
      <div className="max-w-7xl mx-auto">
        <Card className="bg-white border border-gray-200 rounded-xl shadow-sm">
          <CardContent className="p-5">
            {/* Placeholder Content */}
            <div className="flex flex-col items-center justify-center py-24">
              {/* Search Icon Illustration */}
              <div className="w-36 h-32 mb-4 opacity-30">
                <Image src="/images/search-icon.svg" alt="Search Icon" width={144} height={127} />
              </div>

              <p className="text-base text-gray-500 text-center">Start to search tickets</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};
