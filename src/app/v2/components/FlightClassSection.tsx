'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';

export interface FlightClassData {
  isDirectFlight: boolean;
  pointsRequired: number;
  availableSeats: number;
}

interface FlightClassSectionProps {
  className: string;
  data: FlightClassData;
}

export const FlightClassSection: React.FC<FlightClassSectionProps> = ({
  className,
  data,
}) => {
  const formatPoints = (points: number): string => {
    if (points === 0) return '0 points';
    return `${points.toLocaleString()} points`;
  };

  const formatSeats = (seats: number): string => {
    if (seats === 0) return '0 seats';
    return `${seats.toLocaleString()} seats`;
  };

  const getFlightTypeText = (isDirectFlight: boolean): string => {
    return isDirectFlight ? 'Direct Flight' : 'Has Connections';
  };

  return (
    <div className="flex flex-col gap-4 p-5 flex-1 border-r border-gray-200 last:border-r-0">
      {/* Class Name */}
      <h3 className="text-base font-medium text-gray-900">{className}</h3>
      
      {/* Content */}
      <div className="flex flex-col gap-3">
        {/* Flight Type Row */}
        <div className="flex items-center justify-between gap-2">
          <span className="text-sm text-gray-700 w-26 flex-shrink-0">Flight Type</span>
          <Badge
            variant="default"
            size="md"
            shape="default"
            className="shadow-xs"
          >
            {getFlightTypeText(data.isDirectFlight)}
          </Badge>
        </div>

        {/* Point Required Row */}
        <div className="flex items-center justify-between gap-2">
          <span className="text-sm text-gray-700 w-26 flex-shrink-0">Point Required</span>
          <Badge
            variant="warning"
            size="md"
            shape="pill"
          >
            {formatPoints(data.pointsRequired)}
          </Badge>
        </div>

        {/* No.of Seat Row */}
        <div className="flex items-center justify-between gap-2">
          <span className="text-sm text-gray-700 w-26 flex-shrink-0">No.of Seat</span>
          <Badge
            variant="purple"
            size="md"
            shape="pill"
          >
            {formatSeats(data.availableSeats)}
          </Badge>
        </div>
      </div>
    </div>
  );
};
