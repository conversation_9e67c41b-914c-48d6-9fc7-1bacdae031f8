'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { DownloadOutlined, UpOutlined } from '@ant-design/icons';

interface CalendarGridHeaderProps {
  title: string;
  hasFlights: boolean;
  isExpanded: boolean;
  onExport: () => void;
  onToggleExpand: () => void;
}

export const CalendarGridHeader: React.FC<CalendarGridHeaderProps> = ({
  title,
  hasFlights,
  isExpanded,
  onExport,
  onToggleExpand,
}) => {
  return (
    <div className="flex justify-between items-center px-5 py-5">
      <div className="flex items-center gap-2">
        <h2 className="text-lg font-medium text-gray-800">{title}</h2>
      </div>
      
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onExport}
          disabled={!hasFlights}
          className={`flex items-center gap-1 px-3.5 py-2.5 shadow-sm ${
            hasFlights
              ? 'bg-white border border-gray-300 hover:bg-gray-50'
              : 'bg-white border border-gray-300 cursor-not-allowed'
          }`}
          aria-label={`Export ${title} flight data`}
        >
          <DownloadOutlined 
            className={`w-5 h-5 ${hasFlights ? 'text-gray-400' : 'text-gray-300'}`} 
          />
          <span className={`text-sm font-semibold ${hasFlights ? 'text-gray-700' : 'text-gray-400'}`}>
            Export
          </span>
        </Button>
        
        <button
          onClick={onToggleExpand}
          className="flex items-center gap-2 p-2.5 hover:bg-gray-50 rounded-md transition-colors"
          aria-label={isExpanded ? 'Collapse calendar' : 'Expand calendar'}
          type="button"
        >
          <UpOutlined
            className={`w-4 h-4 text-gray-700 transition-transform duration-200 ${
              isExpanded ? 'rotate-0' : 'rotate-180'
            }`}
          />
        </button>
      </div>
    </div>
  );
};
