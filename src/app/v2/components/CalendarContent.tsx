'use client';

import React from 'react';
import { MonthData, chunkArray } from '@/lib/calendar.utils';
import { DayData } from './MonthCalendar';
import { MonthRow } from './MonthRow';
import { NoFlightsDisplay } from './NoFlightsDisplay';
import { MONTHS_PER_ROW } from '@/app/v2/constants';

interface CalendarContentProps {
  months: MonthData[];
  days: DayData[];
  hasFlights: boolean;
  title: string;
}

export const CalendarContent: React.FC<CalendarContentProps> = ({
  months,
  days,
  hasFlights,
  title,
}) => {
  if (!hasFlights) {
    return <NoFlightsDisplay message="No available seat" />;
  }

  const monthRows = chunkArray(months, MONTHS_PER_ROW);

  return (
    <div className="border-t border-gray-200">
      {monthRows.map((monthsInRow, index) => (
        <MonthRow
          key={`row-${index}`}
          months={monthsInRow}
          days={days}
          className={index > 0 ? 'border-t border-gray-200' : ''}
        />
      ))}
    </div>
  );
};
