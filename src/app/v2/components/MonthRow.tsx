'use client';

import React from 'react';
import { MonthCalendar, DayData } from './MonthCalendar';
import { MonthData } from '@/lib/calendar.utils';
import { MONTHS_PER_ROW } from '@/app/v2/constants';

interface MonthRowProps {
  months: MonthData[];
  days: DayData[];
  className?: string;
}

export const MonthRow: React.FC<MonthRowProps> = ({ months, days, className = '' }) => {
  const filterDaysForMonth = (month: MonthData): DayData[] => {
    return days.filter((day) => {
      const dayDate = new Date(day.date);
      return dayDate.getMonth() + 1 === month.month && dayDate.getFullYear() === month.year;
    });
  };

  return (
    <div className={`flex flex-row ${className}`}>
      {months.map((month) => (
        <MonthCalendar
          key={`${month.month}-${month.year}`}
          monthName={month.display}
          month={month.month}
          year={month.year}
          days={filterDaysForMonth(month)}
        />
      ))}
      {/* if months is less than MONTHS_PER_ROW, add empty div to make it MONTHS_PER_ROW */}
      {months.length < MONTHS_PER_ROW &&
        Array.from({ length: MONTHS_PER_ROW - months.length }).map((_, index) => (
          <div key={index} className="flex flex-col justify-center items-center gap-3 p-3 w-full border-r border-gray-200 last:border-r-0"></div>
        ))}
    </div>
  );
};
