'use client';

import React from 'react';
import Image from 'next/image';

export const HeroBanner: React.FC = () => {
  return (
    <section className="px-6 lg:px-12 py-8 lg:py-12 mb-3">
      <div className="max-w-7xl mx-auto flex flex-col items-center justify-center space-y-4">
        {/* Award Spy Logo */}
        <div className="flex items-center justify-center">
          <Image 
            src="/ysf-logo-blue.webp" 
            alt="Award Spy Logo" 
            width={120} 
            height={40}
            className="h-10 w-auto filter invert brightness-0 contrast-100"
          />
        </div>
        
        {/* Power by Award Spy Text */}
        <div className="text-center">
          <p className="text-sm text-white/90 font-medium">Power by Award Spy</p>
        </div>
      </div>
    </section>
  );
};
