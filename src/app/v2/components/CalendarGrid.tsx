'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { generateMonthsInRange } from '@/lib/calendar.utils';
import { DayData } from './MonthCalendar';
import { CalendarGridHeader } from './CalendarGridHeader';
import { CalendarContent } from './CalendarContent';
import { cloneDeep } from 'lodash';
import { format } from 'date-fns';
import ExportModal from '@/components/modals/ExportModal';

interface CalendarGridProps {
  /** Title displayed in the calendar header */
  title?: string;
  /** Array of day data containing seat availability */
  days: DayData[];
  /** Whether flights are available for display */
  hasFlights?: boolean;
  /** Callback function triggered when export button is clicked */
  onExport?: () => void;
}

export const CalendarGrid: React.FC<CalendarGridProps> = ({
  title = 'Business',
  days,
  hasFlights = true,
  onExport,
}) => {
  const [isExpanded, setIsExpanded] = useState(hasFlights);
  const [isOpen, setIsOpen] = useState(false);

  const handleOpenExport = () => {
    setIsOpen(true);
  };

  const handleToggleExpand = useCallback(() => {
    setIsExpanded((prev) => !prev);
  }, []);

  const daysData = cloneDeep(days);
  daysData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  const startDate = daysData.length > 0 ? daysData[0].date : format(new Date(), 'yyyy-MM-dd');
  const endDate = daysData.length > 0 ? daysData[daysData.length - 1].date : format(new Date(), 'yyyy-MM-dd');
  const months =  daysData.length > 0 ? generateMonthsInRange(startDate, endDate) : [];

  return (
    <Card 
      className="bg-white border border-gray-200 rounded-xl shadow-sm"
      role="region"
      aria-label={`${title} flight calendar`}
    >
      <ExportModal isOpen={isOpen} onClose={() => setIsOpen(false)} onExport={handleOpenExport} />
      <CardContent className="p-0">
        <CalendarGridHeader
          title={title}
          hasFlights={hasFlights}
          isExpanded={isExpanded}
          onExport={handleOpenExport}
          onToggleExpand={handleToggleExpand}
        />

        {isExpanded && (
          <CalendarContent
            months={months}
            days={days}
            hasFlights={hasFlights}
            title={title}
          />
        )}
      </CardContent>
    </Card>
  );
};
