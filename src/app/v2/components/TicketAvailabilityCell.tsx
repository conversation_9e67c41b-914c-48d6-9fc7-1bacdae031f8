'use client';

import React, { useMemo } from 'react';

interface TicketAvailabilityCellProps {
  /** Day number (1-31) */
  date: number;
  /** Number of available seats */
  seatCount: number;
}

export const TicketAvailabilityCell: React.FC<TicketAvailabilityCellProps> = ({ 
  date, 
  seatCount 
}) => {
  const styles = useMemo(() => {
    const hasSeats = seatCount > 0;
    return {
      container: hasSeats ? 'bg-blue-500 hover:bg-blue-600' : 'bg-gray-100',
      text: hasSeats ? 'text-white' : 'text-gray-600',
    };
  }, [seatCount]);

  const hasSeats = seatCount > 0;

  return (
    <div
      className={`
        flex justify-center items-center gap-1 p-0.5
        w-full h-8 rounded-md cursor-pointer
        ${styles.container}
        ${hasSeats ? 'transition-colors duration-200 focus:ring-2 focus:ring-blue-300 focus:outline-none' : ''}
      `}
      tabIndex={hasSeats ? 0 : undefined}
      role={hasSeats ? 'button' : undefined}
      aria-label={hasSeats ? `${date} - ${seatCount} seats available` : `${date} - No seats available`}
      onKeyDown={hasSeats ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          // Handle selection logic here if needed
        }
      } : undefined}
    >
      <span className={`text-xs font-normal leading-none ${styles.text}`}>
        {date}
      </span>
      {hasSeats && (
        <span className={`text-xs font-normal leading-none ${styles.text}`}>
          ({seatCount})
        </span>
      )}
    </div>
  );
};
