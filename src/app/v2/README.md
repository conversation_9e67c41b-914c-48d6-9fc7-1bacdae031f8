# Home Page v2 - Award Spy

This is the new version of the home page based on the Figma design.

## 🎨 Design Reference

- **Figma Design**: [Award Spy Search Layout](https://www.figma.com/design/pvUW3O0KXwL5At1toml86F/Award-Spy?node-id=48-44692&t=f6RdDTIQX2wgxBpY-4)
- **Downloaded Designs**: 
  - `public/figma-design/home-page-design.png` (Original overview)
  - `public/figma-design/search-layout-design.png` (Specific search layout)

## 📁 Structure

### V2 Components (`/src/components/v2/`)
- `Header.tsx` - Enhanced header with improved user info display
- `HeroBanner.tsx` - Hero section with compelling messaging
- `SearchSection.tsx` - Main search container with new styling
- `SearchFormV2.tsx` - Enhanced search form matching Figma design
- `ResultSection.tsx` - Results display area (placeholder for now)

### V2 Pages (`/src/app/v2/`)
- `page.tsx` - Main home page using new component structure
- `layout.tsx` - V2-specific layout and metadata

## 🆚 Comparison with V1

### Original Version (`/src/app/page.tsx`)
- Basic gradient background
- Simple header with logo only
- Inline filters component
- Limited visual hierarchy
- Basic card styling

### V2 Version (`/src/app/v2/page.tsx`)
- **Enhanced Header**: Better user information display, responsive design
- **Hero Banner**: Compelling headline and value proposition
- **Improved Search Form**: 
  - Better visual hierarchy
  - Enhanced input styling with icons
  - Trip type selector (Round Trip, One Way, Multi City)
  - Passenger and class selection
  - More intuitive layout
- **Better Card Design**: Rounded corners, backdrop blur, improved shadows
- **Responsive Layout**: Better mobile and desktop experience

## 🎯 Key Improvements

1. **Visual Hierarchy**: Clear section separation and better content flow
2. **User Experience**: More intuitive search form with better labels and icons
3. **Modern Design**: Updated styling with gradients, shadows, and modern UI elements
4. **Responsive**: Better mobile experience with responsive grid layouts
5. **Accessibility**: Better semantic HTML and ARIA labels
6. **Performance**: Optimized component structure for better rendering

## 🚀 Usage

To view the new version, navigate to: `/v2`

To compare with the original: `/` (original) vs `/v2` (new version)

## 🔄 Integration

The v2 components are designed to be:
- **Modular**: Each component has a specific responsibility
- **Reusable**: Components can be used in other parts of the application
- **Extensible**: Easy to add new features and functionality
- **Type-safe**: Full TypeScript support with proper interfaces

## 📋 Next Steps

1. Connect SearchFormV2 to the existing flight search logic
2. Integrate the result display with the new ResultSection component
3. Add filters and advanced search options
4. Implement the trip type functionality (Round Trip, One Way, Multi City)
5. Add animations and micro-interactions for better UX
