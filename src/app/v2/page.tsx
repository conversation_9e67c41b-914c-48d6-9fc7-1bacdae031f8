import { getOriginsAndDestination } from '@/server/actions/getOriginsAndDestination';
import { getServerSession } from 'next-auth/next';
import { redirect } from 'next/navigation';
import { authOptions } from '@/configs/auth';
import { HomePageV2Client } from './components/HomePageV2Client';

// Server component wrapper
const HomePage = async () => {
  const { origins, destinations } = await getOriginsAndDestination();
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/signin');
  }

  return <HomePageV2Client origins={origins} destinations={destinations} session={session} />;
};

export default HomePage;
