import Filters from '@/components/filters';
import { Card, CardContent } from '@/components/ui/card';
import { getOriginsAndDestination } from '@/server/actions/getOriginsAndDestination';
import { getServerSession } from 'next-auth/next';
import SignOutButton from '@/components/auth/SignOutButton';
import Link from 'next/link';
import { redirect } from 'next/navigation';
import { authOptions } from '@/configs/auth';

const BlogPage = async () => {
  const { origins, destinations } = await getOriginsAndDestination();
  const session = await getServerSession(authOptions);
  if (!session) {
    redirect('/signin');
  }
  return (
    <div className="min-h-screen bg-gradient-to-b from-[#87A0FF] to-[#503CB4] flex flex-col font-sans">
      <header className="flex justify-between items-center px-8 py-6">
        <div className="flex items-center space-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 194 49.48" className="h-12 w-auto">
            <defs>
              <style>{`.cls-1{fill:#ffffff;}.cls-2{fill:#ffffff;}.cls-3{fill:#ffffff;}`}</style>
            </defs>
            <title>logo-color0</title>
            <g id="Layer_2" data-name="Layer 2">
              <g id="Layer_1-2" data-name="Layer 1">
                <path
                  className="cls-1"
                  d="M0,22.72c37.66-5,14.63,26.76,14.63,26.76L43.88,13.07ZM17.05,42.56a21.66,21.66,0,0,0,1.67-5.73,11.29,11.29,0,0,0-.86-6.59L17.72,30s1.38,4.69-2.84,11.22A20.84,20.84,0,0,1,6.22,24.28c0-.25,0-.49,0-.73-1.07,0-2.21.1-3.43.21,0,.17,0,.34,0,.52A24.27,24.27,0,0,0,15.51,45.62s.88-1.68,1-1.82C16.67,43.39,16.86,43,17.05,42.56Z"
                />
                <path
                  className="cls-2"
                  d="M18.39,47A24.28,24.28,0,1,0,3.07,20.66l3.62-.79v0l28.47-6.46L7.56,16.94a20.85,20.85,0,0,1,36.17-5.22l3.6-.79-2.26,2.81a20.85,20.85,0,0,1-18,31.39A20.2,20.2,0,0,1,25,45L40.54,20.84Z"
                />
                <path className="cls-3" d="M64.27,16.28V4.85h1.94V16.28Z" />
                <path
                  className="cls-3"
                  d="M94.15,10.56a6.12,6.12,0,0,1-12.22,0,6.12,6.12,0,0,1,12.22,0Zm-10.24,0a4.12,4.12,0,0,0,8.24,0,4.12,4.12,0,0,0-8.24,0Z"
                />
                <path
                  className="cls-3"
                  d="M109,16.28l-6.14-8.2v8.2h-2V4.85h1.91L109,13.06V4.85h1.91V16.28Z"
                />
                <path className="cls-3" d="M125.6,14.53v1.75h-6.94V4.85h2v9.68Z" />
                <path
                  className="cls-3"
                  d="M136.88,16.28H135V12.54l-4.29-7.69h2l3.29,5.57,3.23-5.57h2l-4.23,7.62Z"
                />
                <path className="cls-3" d="M158.23,6.58v3.3h5.24v1.71h-5.24v4.69h-1.94V4.85H164l0,1.73Z" />
                <path className="cls-3" d="M177.38,14.53v1.75h-6.93V4.85h2v9.68Z" />
                <path
                  className="cls-3"
                  d="M188.67,16.28h-1.93V12.54l-4.29-7.69h2l3.29,5.57,3.23-5.57h2l-4.23,7.62Z"
                />
                <path className="cls-2" d="M68.39,30.14v4.45h8.24V38H68.39v6.85H64.27V26.76H77.39l0,3.38Z" />
                <path
                  className="cls-2"
                  d="M83.88,27a2.17,2.17,0,1,1-2.16-2.22A2.1,2.1,0,0,1,83.88,27ZM79.71,44.85V31h4v13.9Z"
                />
                <path
                  className="cls-2"
                  d="M91.6,33.45a5.14,5.14,0,0,1,4.65-2.66v3.75c-2.87-.26-4.65,1.27-4.65,3.62v6.69h-4V31h4Z"
                />
                <path
                  className="cls-2"
                  d="M108.66,32.32,107.28,35A9.61,9.61,0,0,0,103,33.69c-.94,0-1.62.31-1.62,1,0,2.09,7.41,1,7.38,6,0,2.84-2.53,4.34-5.71,4.34A9.81,9.81,0,0,1,97,43.07l1.3-2.69a8.51,8.51,0,0,0,4.88,1.73c1,0,1.77-.33,1.77-1.08,0-2.23-7.27-1-7.27-5.92,0-2.87,2.47-4.35,5.52-4.35A10.47,10.47,0,0,1,108.66,32.32Z"
                />
                <path
                  className="cls-2"
                  d="M120.14,44.08a7.6,7.6,0,0,1-3.65,1c-2.51,0-4.41-1.42-4.41-4.29V34.31h-2V31.57h2V27.74h4v3.83h3.81v2.76h-3.81v5.74c0,1.16.49,1.63,1.35,1.6a4.75,4.75,0,0,0,1.88-.51Z"
                />
                <path
                  className="cls-2"
                  d="M144.55,29.63l-2.4,2.89a6.63,6.63,0,0,0-4.75-2.35,5.57,5.57,0,0,0,0,11.14,7.07,7.07,0,0,0,4.75-2.12l2.42,2.61A10.71,10.71,0,0,1,137.19,45a9.22,9.22,0,0,1-9.57-9.25c0-5.22,4.2-9.15,9.73-9.15A10.43,10.43,0,0,1,144.55,29.63Z"
                />
                <path className="cls-2" d="M147.08,44.85V25.67h4V44.85Z" />
                <path
                  className="cls-2"
                  d="M163.25,43.22A5.4,5.4,0,0,1,158.84,45c-3,0-4.82-1.81-4.82-4.34s1.9-4.16,5.34-4.19h3.86v-.26c0-1.47-1-2.32-2.92-2.32a8.94,8.94,0,0,0-4.17,1.21l-1.25-2.74a13.25,13.25,0,0,1,6.33-1.6c3.76,0,5.92,1.86,5.95,5l0,9.07h-3.94Zm0-3.12V38.86h-3.15c-1.54,0-2.3.46-2.3,1.57s.84,1.76,2.19,1.76C161.66,42.19,163,41.29,163.22,40.1Z"
                />
                <path
                  className="cls-2"
                  d="M180.88,32.32,179.5,35a9.61,9.61,0,0,0-4.28-1.34c-.94,0-1.62.31-1.62,1,0,2.09,7.41,1,7.38,6,0,2.84-2.53,4.34-5.71,4.34a9.81,9.81,0,0,1-6.07-1.94l1.3-2.69a8.51,8.51,0,0,0,4.88,1.73c1,0,1.77-.33,1.77-1.08,0-2.23-7.27-1-7.27-5.92,0-2.87,2.47-4.35,5.52-4.35A10.47,10.47,0,0,1,180.88,32.32Z"
                />
                <path
                  className="cls-2"
                  d="M193.9,32.32,192.51,35a9.57,9.57,0,0,0-4.27-1.34c-.94,0-1.62.31-1.62,1,0,2.09,7.41,1,7.38,6,0,2.84-2.53,4.34-5.71,4.34a9.84,9.84,0,0,1-6.08-1.94l1.31-2.69a8.49,8.49,0,0,0,4.87,1.73c1,0,1.78-.33,1.78-1.08,0-2.23-7.28-1-7.28-5.92,0-2.87,2.48-4.35,5.53-4.35A10.49,10.49,0,0,1,193.9,32.32Z"
                />
              </g>
            </g>
          </svg>
        </div>
      </header>

      <main className="flex-1 flex items-start justify-center px-4 pb-12">
        <div className="w-full max-w-6xl">
          <div style={{ maxWidth: '400px', margin: '50px auto' }}>
            <h1>Home page</h1>
            {session ? (
              <div>
                <p>Login with: {session.user?.name || session.user?.email}</p>
                <p>Email: {session.user?.email}</p>
                <SignOutButton />
              </div>
            ) : (
              <p>
                You are not logged in. <Link href="/signin">Login</Link>
              </p>
            )}
          </div>
          <Card className="bg-white/95 backdrop-blur-sm border-0 shadow-2xl">
            <CardContent>
              <Filters origins={origins} destinations={destinations} />
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default BlogPage;
