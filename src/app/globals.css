@tailwind base;
@tailwind components;
@tailwind utilities;

/* Export Modal Styling to match Figma design */
.export-modal .ant-modal-content {
  border-radius: 16px !important;
  overflow: hidden !important;
  padding: 0 !important;
  box-shadow: 0px 3px 3px -1.5px rgba(10, 13, 18, 0.04), 
              0px 8px 8px -4px rgba(10, 13, 18, 0.03), 
              0px 20px 24px -4px rgba(10, 13, 18, 0.08) !important;
}

.export-modal .ant-modal-close {
  top: 16px !important;
  right: 16px !important;
  width: 44px !important;
  height: 44px !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.export-modal .ant-modal-close:hover {
  background-color: #f5f5f5 !important;
}

.export-modal .ant-input {
  border-color: #d5d7da !important;
  box-shadow: 0px 1px 2px 0px rgba(10, 13, 18, 0.05) !important;
  font-size: 16px !important;
}

.export-modal .ant-input:focus {
  border-color: #1677ff !important;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1) !important;
}

.export-modal .ant-select .ant-select-selector {
  border-color: #d5d7da !important;
  box-shadow: 0px 1px 2px 0px rgba(10, 13, 18, 0.05) !important;
  font-size: 16px !important;
}

.export-modal .ant-select-focused .ant-select-selector {
  border-color: #1677ff !important;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1) !important;
}

.export-modal .ant-switch {
  background-color: rgba(0,0,0,0.25) !important;
}

.export-modal .ant-switch-checked {
  background-color: #1677ff !important;
}

@layer base {
  :root {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 340 82% 52%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 340 82% 52%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
}
.list-calendar{
  position: relative;
}
.ysf-logo{
  position: absolute;
  right: 20px;
  bottom: 10px;
  z-index: 12;
}
.ysf-logo svg{
  width: 50px;
}
.list-calendar-bg {
  background: url("/ysf-logo-blue.webp") no-repeat center;
  background-size: contain;
  opacity: 0.15;
  position: absolute;
  width: 70%;
  height: 70%;
  bottom: 25px;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
}
.list-calendar-month {
  text-transform: uppercase;
  font-size: 18px;
  text-align: center;
  color: #ec4899;
  font-weight: bold;
}
.list-calendar-col:nth-child(2n) .list-calendar-month {
  color: #166078;
}
.list-calendar-col {
  border-top: 1px solid #dedede;
  border-right: 1px solid #dedede;
  padding: 10px 10px 20px;
  min-height: 300px;
}
.list-calendar-col:nth-child(4n) {
  border-right: 0;
}
.list-calendar-col:nth-child(1),
.list-calendar-col:nth-child(2),
.list-calendar-col:nth-child(3),
.list-calendar-col:nth-child(4) {
  border-top: 0;
}
