'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { FeaturedIcon } from '@/components/ui/featured-icon';
import { LoginButton } from '@/components/auth/LoginButton';
import Image from 'next/image';
import { signIn } from 'next-auth/react';
import { IoMdArrowBack } from "react-icons/io";

export default function SignInPage() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [emailError, setEmailError] = useState('');

  const [isLoginSuccess, setIsLoginSuccess] = useState(true);

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newEmail = e.target.value;
    setEmail(newEmail);

    // Clear previous errors
    setEmailError('');
    setError('');

    // Validate email if it's not empty
    if (newEmail && !validateEmail(newEmail)) {
      setEmailError('Please enter a valid email address');
    }
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate email before submission
    if (!email) {
      setEmailError('Email address is required');
      return;
    }

    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }

    // Clear any validation errors
    setEmailError('');
  };

  const handleLogin = async () => {
    if (!email) return;

    setIsLoading(true);

    try {
      const result = await signIn('email', {
        email,
        callbackUrl: '/',
        redirect: false, // Don't redirect automatically so we can handle errors
      });
      if (result?.error) {
        console.log(result.error);
        // Handle specific authentication errors
        setError('This email is not authorized to access the system. Please contact your administrator.');
      } else if (result?.ok) {
        // Show success card when email is sent successfully
        setIsLoginSuccess(true);
      }
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    setIsLoginSuccess(false);
    setEmail('');
    setError('');
    setEmailError('');
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#2259C8] via-[#2259C8] to-[#1344A7] flex flex-col justify-center items-center p-4 relative">
      {/* World Map Background - positioned absolutely and lower opacity */}
      <div
        className="absolute inset-0 h-full w-full opacity-100"
        style={{
          backgroundImage: `url('/images/world-map.svg')`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'contain',
          backgroundPosition: 'center',
        }}
      />

      {/* Logo positioned at top */}
      <div className="flex justify-center items-center pb-16 w-full max-w-[400px] relative z-10">
        <div className="w-[188px] h-12">
          <Image src="/images/logo.svg" alt="logo" width={188} height={48} />
        </div>
      </div>

      {/* Main Content Card */}
      {!isLoginSuccess && (
        <div className="w-full max-w-[400px] bg-white rounded-xl flex flex-col items-center gap-5 p-6 relative z-10">
          {/* Header Section with Featured Icon */}
          <div className="flex items-center gap-4 w-full">
            <FeaturedIcon size="xl">
              <Image src="/images/mail-icon.svg" alt="mail" width={18} height={16} />
            </FeaturedIcon>

            <div className="flex flex-col justify-center items-center gap-1">
              <h1 className="text-lg font-medium text-[#181D27] leading-[28px]">Login With Your Email</h1>
            </div>
          </div>

          {/* Form Section */}
          <form onSubmit={handleFormSubmit} className="w-full flex flex-col gap-6">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">{error}</div>
            )}

            {/* Email Input Field */}
            <div className="flex flex-col gap-[6px] w-full">
              {/* Label with Required Asterisk */}
              <div className="flex gap-0.5">
                <label htmlFor="email" className="text-sm font-medium text-[#414651] leading-5">
                  Email Address
                </label>
                <span className="text-sm font-medium text-[#7F56D9]">*</span>
              </div>

              {/* Input Field */}
              <div
                className={`flex items-center gap-2 px-3 py-2 bg-white border rounded-lg shadow-sm w-full ${
                  emailError
                    ? 'border-red-300 focus-within:border-red-500'
                    : 'border-[#D5D7DA] focus-within:border-[#7F56D9]'
                }`}
              >
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={handleEmailChange}
                  required
                  className="flex-1 text-base text-[#717680] bg-white border-0 shadow-none focus-visible:ring-0 focus-visible:ring-offset-0 p-0 h-auto"
                />
              </div>

              {/* Email Error Message */}
              {emailError && <div className="text-sm text-red-600 mt-1">{emailError}</div>}
            </div>

            {/* Continue Button */}
            {email && validateEmail(email) && !emailError ? (
              <Button
                onClick={handleLogin}
                disabled={isLoading || !email}
                className="w-full flex justify-center items-center gap-1 px-[14px] py-[10px] bg-[#155EEF] hover:bg-[##155EEF] border-0 rounded-lg shadow-sm text-sm font-semibold text-white disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Signing In...' : 'Continue'}
              </Button>
            ) : (
              <Button
                type="button"
                disabled={true}
                className="w-full flex justify-center items-center gap-1 px-[14px] py-[10px] bg-[#F5F5F5] border border-[#E9EAEB] rounded-lg shadow-sm text-sm font-semibold text-[#A4A7AE] disabled:cursor-not-allowed"
              >
                Continue
              </Button>
            )}
          </form>
        </div>
      )}
      {isLoginSuccess && (
        <div className="w-full max-w-[424px] bg-white rounded-xl flex flex-col items-center gap-5 p-6 relative z-10">
          {/* Back Arrow Button */}
          <button
            onClick={handleBackToLogin}
            className="absolute top-2 left-2 flex items-center gap-2 p-2.5 hover:bg-gray-50 rounded-lg transition-colors"
            aria-label="Go back to login"
          >
            <IoMdArrowBack size={20} />
          </button>

          {/* Main Content Wrapper */}
          <div className="flex flex-col items-center gap-4 w-full">
            {/* Featured Icon with Mail */}
            <div className="relative">
              <FeaturedIcon size="xl">
                <Image src="/images/mail-icon.svg" alt="mail" width={20} height={16} className="text-white" />
              </FeaturedIcon>
            </div>

            {/* Content Section */}
            <div className="flex flex-col items-center gap-2 w-full">
              <h1 className="text-lg font-medium text-[#181D27] leading-7">Check Your Email</h1>
              <p className="text-base text-[#414651] text-center leading-6 max-w-[376px]">
                We&apos;ve sent a login link to your email, please click the link in your email to continue.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
