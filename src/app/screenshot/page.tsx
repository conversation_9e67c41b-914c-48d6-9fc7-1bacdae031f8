"use client";
import { FinalFlightsresponseExport } from "@/components/flightsresultsExport";
import { ExportQuery, SearchResultRender } from "@/types/filters.types";
import React from "react";
import { useState, useEffect } from "react";

const ScreenShotPage: React.FC<any> = () => {
  const [searchQuery, setSearchQuery] = useState<ExportQuery | null>(null);
  const [searchResults, setSearchResults] = useState<SearchResultRender | null>(null);


  useEffect(() => {
    const query = localStorage.getItem("searchQuery");
    const results = localStorage.getItem("searchResults");
    if (query) setSearchQuery(JSON.parse(query));
    if (results) setSearchResults(JSON.parse(results));
   
  }, []);
  // console.log(searchQuery);
  // console.log("searchResultssss:", searchResults);
  return <FinalFlightsresponseExport searchResult={searchResults} selectedSource={searchQuery?.selectedSource} selectedAirlines={searchQuery?.selectedAirlines} router={searchQuery?.router} isDirect={searchQuery?.isDirect} typeFly={searchQuery?.typeAir} />;
};

export default ScreenShotPage;
