import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  console.log('Middleware: Processing path:', pathname);

  // For now, let's just log and allow everything to pass through
  // This will help us see the flow without interference
  console.log('Middleware: Allowing all paths for debugging');
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Only protect the home page for now to test
     */
    '/',
    '/screenshot',
  ],
};
