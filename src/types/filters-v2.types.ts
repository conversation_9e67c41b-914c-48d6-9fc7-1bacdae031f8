export interface FlightByDate {
  date: string;
  seatCount: number;
}

export interface FilteredFlightClassData {
  summary: {
    pointsRequired: number;
    availableSeats: number;
  };
  flightByDate: FlightByDate[];
}

export interface FilteredFlightData {
  Economy: FilteredFlightClassData;
  Business: FilteredFlightClassData;
  Premium: FilteredFlightClassData;
  First: FilteredFlightClassData;
}

export interface FilterStateV2 {
  originAirport: string;
  destinationAirport: string;
  selectedAirlines: string[];
  selectedSource: string[];
  isDirect: boolean;
  maxFilterPoints?: number;
  minFilterPoints?: number;
}
