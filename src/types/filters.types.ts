import { FlightClass } from '@/enums/flight-class.enum';
import { AeroFlightItem } from '@/interfaces/aero-search.response';

export interface SearchResultRender {
  Economy: Record<string, Record<string, AeroFlightItem[]>>;
  Business: Record<string, Record<string, AeroFlightItem[]>>;
  Premium: Record<string, Record<string, AeroFlightItem[]>>;
  First: Record<string, Record<string, AeroFlightItem[]>>;
}
export interface FilterState {
  originAirport: string;
  destinationAirport: string;
  selectedAirlines: string[];
  selectedSource: string[];
  exportClass: FlightClass;
  isDirect: boolean;
  filterPoints?: number;
  maxFilterMileageCost?: number;
}

export interface ExportQuery {
  selectedSource: string[];
  destinationAirports: string;
  selectedAirlines: string[];
  router: any;
  isDirect: boolean;
  points: string;
  typeAir: FlightClass;
}

export interface FiltersProps {
  origins: string[];
  destinations: string[];
}
