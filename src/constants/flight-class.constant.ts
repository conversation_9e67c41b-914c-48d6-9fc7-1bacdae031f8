
import { AeroFlightItem } from '@/interfaces/aero-search.response';
import { FlightClassConfig } from '@/types/flight-class-config.type';

// Flight class configuration mapping
export const FLIGHT_CLASS_CONFIG: Record<string, FlightClassConfig> = {
  Economy: {
    remainingSeats: 'YRemainingSeats' as keyof AeroFlightItem,
    available: 'YAvailable' as keyof AeroFlightItem,
    mileageCost: 'YMileageCostRaw' as keyof AeroFlightItem,
    directMileageCost: 'YDirectMileageCostRaw' as keyof AeroFlightItem,
    airlines: 'YAirlines' as keyof AeroFlightItem,
    direct: 'YDirect' as keyof AeroFlightItem,
  },
  Business: {
    remainingSeats: 'JRemainingSeats' as keyof AeroFlightItem,
    available: 'JAvailable' as keyof AeroFlightItem,
    mileageCost: 'JMileageCostRaw' as keyof AeroFlightItem,
    directMileageCost: 'JDirectMileageCostRaw' as keyof AeroFlightItem,
    airlines: 'JA<PERSON><PERSON>' as keyof AeroFlightItem,
    direct: 'JDirect' as keyof AeroFlightItem,
  },
  Premium: {
    remainingSeats: 'WRemainingSeats' as keyof AeroFlightItem,
    available: 'WAvailable' as keyof AeroFlightItem,
    mileageCost: 'WMileageCostRaw' as keyof AeroFlightItem,
    directMileageCost: 'WDirectMileageCostRaw' as keyof AeroFlightItem,
    airlines: 'WAirlines' as keyof AeroFlightItem,
    direct: 'WDirect' as keyof AeroFlightItem,
  },
  First: {
    remainingSeats: 'FRemainingSeats' as keyof AeroFlightItem,
    available: 'FAvailable' as keyof AeroFlightItem,
    mileageCost: 'FMileageCostRaw' as keyof AeroFlightItem,
    directMileageCost: 'FDirectMileageCostRaw' as keyof AeroFlightItem,
    airlines: 'FAirlines' as keyof AeroFlightItem,
    direct: 'FDirect' as keyof AeroFlightItem,
  },
};

export const EXPORT_CLASS_OPTIONS = [
  { value: 'Economy', label: 'Economy' },
  { value: 'Business', label: 'Business' },
  { value: 'Premium', label: 'Premium' },
  { value: 'First', label: 'First' },
];
