import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { cloneDeep } from 'lodash';

import { AeroFlightItem } from '@/interfaces/aero-search.response';
import { SearchResultRender, FilterState } from '@/types/filters.types';
import { FLIGHT_CLASS_CONFIG } from '@/constants/flight-class.constant';
import { getFlight } from '@/server/actions/getFlight';

export const useFlightSearch = () => {
  const [searchResult, setSearchResult] = useState<AeroFlightItem[]>([]);
  const [searchResultRender, setSearchResultRender] = useState<SearchResultRender>({
    Economy: {},
    Business: {},
    Premium: {},
    First: {},
  });
  const [sources, setSources] = useState<string[]>([]);
  const [airlines, setAirlines] = useState<string[]>([]);
  const [router, setRouter] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [isShowFilter, setIsShowFilter] = useState<boolean>(false);

  // Group flights by date for display
  const groupResultsByDate = (
    flights: AeroFlightItem[]
  ): Record<string, Record<string, AeroFlightItem[]>> => {
    return flights.reduce((monthDateGroups, flight) => {
      const flightDate = new Date(flight.Date);
      const monthName = flightDate.toLocaleString('en-us', { month: 'long' });
      const dateKey = flight.Date;

      if (!monthDateGroups[monthName]) {
        monthDateGroups[monthName] = {};
      }

      if (!monthDateGroups[monthName][dateKey]) {
        monthDateGroups[monthName][dateKey] = [];
      }

      monthDateGroups[monthName][dateKey].push(flight);
      return monthDateGroups;
    }, {} as Record<string, Record<string, AeroFlightItem[]>>);
  };

  // Filter flights for a specific class
  const filterFlightsForClass = (
    flights: AeroFlightItem[],
    classKey: keyof typeof FLIGHT_CLASS_CONFIG,
    filters: FilterState,
    includePoints: boolean = false
  ): AeroFlightItem[] => {
    const config = FLIGHT_CLASS_CONFIG[classKey];

    return flights.filter((item) => {
      // Filter by available seats
      const hasSeats = (item[config.remainingSeats] as number) > 0 || (item[config.available] as boolean);
      if (!hasSeats) return false;

      // Filter by valid mileage cost
      const hasValidMileageCost = !isNaN(parseFloat(item[config.mileageCost] as string));
      if (!hasValidMileageCost) return false;

      // Filter by selected airlines
      if (filters.selectedAirlines?.length > 0) {
        const hasSelectedAirline =
          filters.selectedAirlines.some((airline: string) => (item[config.airlines] as string)?.includes(airline)) ||
          (item[config.available] as boolean);
        if (!hasSelectedAirline) return false;
      }

      // Filter by selected source
      if (filters.selectedSource?.length > 0) {
        const hasSelectedSource = filters.selectedSource.some((source: string) => item.Source?.includes(source));
        if (!hasSelectedSource) return false;
      }

      // Filter by direct flights only
      if (filters.isDirect && !(item[config.direct] as boolean)) {
        return false;
      }

      if (includePoints && filters.filterPoints) {
        // Filter by points limit
        const cost = filters.isDirect ? Number(item[config.directMileageCost]) : Number(item[config.mileageCost]);
        if (cost > filters.filterPoints) {
          return false;
        }
      }

      return true;
    });
  };

  // Find max mileage cost for a class
  const findMaxMileageCost = (
    flights: AeroFlightItem[],
    classKey: keyof typeof FLIGHT_CLASS_CONFIG,
    isDirect: boolean
  ): number => {
    const config = FLIGHT_CLASS_CONFIG[classKey];

    if (flights.length === 0) return 0;

    return flights.reduce((max, item) => {
      const cost = isDirect ? Number(item[config.directMileageCost]) : Number(item[config.mileageCost]);
      return cost > max ? cost : max;
    }, 0);
  };

  // Set render data for all flight classes
  const setRenderData = (data: AeroFlightItem[], filters: FilterState) => {
    const processedClasses = Object.keys(FLIGHT_CLASS_CONFIG).reduce((acc, classKey) => {
      const typedClassKey = classKey as keyof typeof FLIGHT_CLASS_CONFIG;
      acc[typedClassKey] = filterFlightsForClass(cloneDeep(data), typedClassKey, filters, true);
      return acc;
    }, {} as Record<keyof typeof FLIGHT_CLASS_CONFIG, AeroFlightItem[]>);

    setSearchResultRender({
      Economy: groupResultsByDate(processedClasses.Economy),
      Business: groupResultsByDate(processedClasses.Business),
      Premium: groupResultsByDate(processedClasses.Premium),
      First: groupResultsByDate(processedClasses.First),
    });

    if (data.length > 0) {
      setRouter(data[0].Route);
    }
  };

  // Calculate maximum mileage cost across all classes
  const calculateMaxMileageCost = useCallback((data: AeroFlightItem[], filters: FilterState) => {
    if (filters.selectedAirlines.length === 0 || filters.selectedSource.length === 0) {
      return 0;
    }

    const processedClasses = Object.keys(FLIGHT_CLASS_CONFIG).reduce((acc, classKey) => {
      const typedClassKey = classKey as keyof typeof FLIGHT_CLASS_CONFIG;
      acc[typedClassKey] = filterFlightsForClass(cloneDeep(data), typedClassKey, filters);
      return acc;
    }, {} as Record<keyof typeof FLIGHT_CLASS_CONFIG, AeroFlightItem[]>);

    const maxMileageCosts = Object.keys(FLIGHT_CLASS_CONFIG).map((classKey) =>
      findMaxMileageCost(
        processedClasses[classKey as keyof typeof FLIGHT_CLASS_CONFIG],
        classKey as keyof typeof FLIGHT_CLASS_CONFIG,
        filters.isDirect
      )
    );

    return Math.max(...maxMileageCosts);
  }, []);

  // Fetch flight data and extract sources/airlines
  const fetchFlightData =  useCallback ( async(originAirport: string, destinationAirport: string) => {
    try {
      if (!originAirport || !destinationAirport) {
        return;
      }

      const res = await getFlight(originAirport, destinationAirport);
      const data = res.data || res;
      setSearchResult(data);

      // Extract sources
      const allSources = data.reduce((acc: string[], flight: AeroFlightItem) => {
        if (!acc.includes(flight.Route.Source)) {
          acc.push(flight.Route.Source);
        }
        return acc;
      }, []);
      setSources(allSources.sort());

      // Extract airlines
      const airlineSet = new Set<string>();
      data
        .filter(
          (item: AeroFlightItem) =>
            item.YRemainingSeats + item.JRemainingSeats + item.WRemainingSeats + item.FRemainingSeats > 0
        )
        .forEach((item: AeroFlightItem) => {
          const listAir = `${item.YAirlines},${item.WAirlines},${item.JAirlines},${item.FAirlines}`;
          listAir.split(',').forEach((airline) => {
            if (airline && airline !== 'null') {
              airlineSet.add(airline.trim());
            }
          });
        });

      setAirlines(Array.from(airlineSet).sort());
    } catch (error) {
      console.error('Error fetching flight data:', error);
      message.error('Error fetching flight data. Please try again.');
    }
  }, []);

  // Handle filter result processing
  const handleFilterResult = (filters: FilterState) => {
    console.log('handleFilterResult');
    setLoading(true);
    
    try {
      if (!filters.originAirport || !filters.destinationAirport) {
        return;
      }
      
      if (searchResult.length === 0) {
        return;
      }
      
      setRenderData(searchResult, filters);
      setIsShowFilter(true);
    } catch (error) {
      console.error('Error in search:', error);
      message.error('An error occurred during search. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const resetRenderData = () => {
    setSearchResultRender({
      Economy: {},
      Business: {},
      Premium: {},
      First: {},
    });
    setRouter(null);
    setSearchResult([]);
    setSources([]);
    setAirlines([]);
    setIsShowFilter(false);
  };

  return {
    // State
    searchResult,
    searchResultRender,
    sources,
    airlines,
    router,
    loading,
    isShowFilter,
    
    // Functions
    fetchFlightData,
    handleFilterResult,
    calculateMaxMileageCost,
    setIsShowFilter,
    resetRenderData,
  };
};
