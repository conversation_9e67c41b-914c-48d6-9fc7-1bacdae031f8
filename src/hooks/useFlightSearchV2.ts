import { message } from 'antd';
import { cloneDeep } from 'lodash';
import { useCallback, useState } from 'react';

import { FLIGHT_CLASS_CONFIG } from '@/constants/flight-class.constant';
import { AeroFlightItem } from '@/interfaces/aero-search.response';
import { getFlight } from '@/server/actions/getFlight';
import {
  FilterStateV2,
  FilteredFlightClassData,
  FilteredFlightData,
  FlightByDate
} from '@/types/filters-v2.types';

export const useFlightSearchV2 = () => {
  const [sources, setSources] = useState<string[]>([]);
  const [airlines, setAirlines] = useState<string[]>([]);

  const [searchResult, setSearchResult] = useState<AeroFlightItem[]>([]);
  const [filteredFlightData, setFilteredFlightData] = useState<FilteredFlightData | null>(null);
  const [maxFilterMileageCost, setMaxFilterMileageCost] = useState<number>(0);

  const [isLoadingFlightData, setIsLoadingFlightData] = useState<boolean>(true);
  const [isLoadingFilteredData, setIsLoadingFilteredData] = useState<boolean>(true);

  // Filter flights for a specific class
  const filterFlightsForClass = (
    flights: AeroFlightItem[],
    classKey: keyof typeof FLIGHT_CLASS_CONFIG,
    filters: FilterStateV2,
    includeFilterPoints: boolean = false
  ): AeroFlightItem[] => {
    const config = FLIGHT_CLASS_CONFIG[classKey];

    return flights.filter((item) => {
      // Filter by available seats
      const hasSeats = (item[config.remainingSeats] as number) > 0 || (item[config.available] as boolean);
      if (!hasSeats) return false;

      // Filter by valid mileage cost
      const hasValidMileageCost = !isNaN(parseFloat(item[config.mileageCost] as string));
      if (!hasValidMileageCost) return false;

      // Filter by selected airlines
      if (filters.selectedAirlines?.length > 0) {
        const hasSelectedAirline =
          filters.selectedAirlines.some((airline: string) =>
            (item[config.airlines] as string)?.includes(airline)
          ) || (item[config.available] as boolean);
        if (!hasSelectedAirline) return false;
      }

      // Filter by selected source
      if (filters.selectedSource?.length > 0) {
        const hasSelectedSource = filters.selectedSource.some((source: string) =>
          item.Source?.includes(source)
        );
        if (!hasSelectedSource) return false;
      }

      // Filter by direct flights only
      if (filters.isDirect && !(item[config.direct] as boolean)) {
        return false;
      }

      if (includeFilterPoints) {
        // Filter by points limit
        const cost = filters.isDirect
          ? Number(item[config.directMileageCost])
          : Number(item[config.mileageCost]);
        if (filters.maxFilterPoints && cost > filters.maxFilterPoints) {
          return false;
        }
        if (filters.minFilterPoints && cost < filters.minFilterPoints) {
          return false;
        }
      }

      return true;
    });
  };

  // Find max mileage cost for a class
  const findMaxMileageCost = (
    flights: AeroFlightItem[],
    classKey: keyof typeof FLIGHT_CLASS_CONFIG,
    isDirect: boolean
  ): number => {
    const config = FLIGHT_CLASS_CONFIG[classKey];

    if (flights.length === 0) return 0;

    return flights.reduce((max, item) => {
      const cost = isDirect ? Number(item[config.directMileageCost]) : Number(item[config.mileageCost]);
      return cost > max ? cost : max;
    }, 0);
  };

  // Calculate maximum mileage cost across all classes
  const calculateMaxMileageCost = useCallback((data: AeroFlightItem[], filters: FilterStateV2) => {
    if (filters.selectedAirlines.length === 0 || filters.selectedSource.length === 0) {
      return 0;
    }

    const processedClasses = Object.keys(FLIGHT_CLASS_CONFIG).reduce((acc, classKey) => {
      const typedClassKey = classKey as keyof typeof FLIGHT_CLASS_CONFIG;
      acc[typedClassKey] = filterFlightsForClass(cloneDeep(data), typedClassKey, filters);
      return acc;
    }, {} as Record<keyof typeof FLIGHT_CLASS_CONFIG, AeroFlightItem[]>);

    const maxMileageCosts = Object.keys(FLIGHT_CLASS_CONFIG).map((classKey) =>
      findMaxMileageCost(
        processedClasses[classKey as keyof typeof FLIGHT_CLASS_CONFIG],
        classKey as keyof typeof FLIGHT_CLASS_CONFIG,
        filters.isDirect
      )
    );

    return Math.max(...maxMileageCosts);
  }, []);

  const handleUpdateMaxFilterMileageCost = useCallback(
    (filters: FilterStateV2) => {
      setMaxFilterMileageCost(calculateMaxMileageCost(searchResult, filters));
    },
    [calculateMaxMileageCost, searchResult]
  );

  // Fetch flight data and extract sources/airlines
  const fetchFlightData = useCallback(async (originAirport: string, destinationAirport: string) => {
    try {
      if (!originAirport || !destinationAirport) {
        return;
      }
      setIsLoadingFlightData(true);

      const res = await getFlight(originAirport, destinationAirport);
      const data = res.data || res;
      setSearchResult(data);

      // Extract sources
      const allSources = data.reduce((acc: string[], flight: AeroFlightItem) => {
        if (!acc.includes(flight.Route.Source)) {
          acc.push(flight.Route.Source);
        }
        return acc;
      }, []);
      setSources(allSources.sort());

      // Extract airlines
      const airlineSet = new Set<string>();
      data
        .filter(
          (item: AeroFlightItem) =>
            item.YRemainingSeats + item.JRemainingSeats + item.WRemainingSeats + item.FRemainingSeats > 0
        )
        .forEach((item: AeroFlightItem) => {
          const listAir = `${item.YAirlines},${item.WAirlines},${item.JAirlines},${item.FAirlines}`;
          listAir.split(',').forEach((airline) => {
            if (airline && airline !== 'null') {
              airlineSet.add(airline.trim());
            }
          });
        });

      setAirlines(Array.from(airlineSet).sort());
    } catch (error) {
      console.error('Error fetching flight data:', error);
      message.error('Error fetching flight data. Please try again.');
    } finally {
      setIsLoadingFlightData(false);
    }
  }, []);

  // Handle filter result processing
  const handleFilterResult = (filters: FilterStateV2) => {
    console.log('handleFilterResult');
    setIsLoadingFilteredData(true);

    try {
      if (!filters.originAirport || !filters.destinationAirport) {
        return;
      }

      if (searchResult.length === 0) {
        return;
      }
      handleSetFilteredData(searchResult, filters);
    } catch (error) {
      console.error('Error in search:', error);
      message.error('An error occurred during search. Please try again.');
    } finally {
      setIsLoadingFilteredData(false);
    }
  };

  const calculateFlightClassData = (
    flights: AeroFlightItem[],
    classKey: keyof typeof FLIGHT_CLASS_CONFIG,
    filters: FilterStateV2
  ): FilteredFlightClassData => {
    if (flights.length === 0) {
      return {
        summary: {
          availableSeats: 0,
          pointsRequired: 0,
        },
        flightByDate: [],
      };
    }
    const config = FLIGHT_CLASS_CONFIG[classKey];

    let availableSeats = 0;
    const cost = filters.isDirect
    ? (flights[0][config.directMileageCost] as number)
    : (flights[0][config.mileageCost] as number);
    let pointsRequired = cost;
    const flightByDates: Record<string, FlightByDate> = {};

    flights.forEach((flight) => {
      const date = flight.Date;
      if (!flightByDates[date]) {
        flightByDates[date] = { date, seatCount: 0 };
      }
      flightByDates[date].seatCount += flight[config.remainingSeats] as number;
      availableSeats += flight[config.remainingSeats] as number;

      const cost = filters.isDirect
        ? (flight[config.directMileageCost] as number)
        : (flight[config.mileageCost] as number);

      pointsRequired = Math.min(pointsRequired, cost);
    });

    return {
      summary: {
        availableSeats: availableSeats,
        pointsRequired: pointsRequired,
      },
      flightByDate: Object.values(flightByDates),
    };
  };

  const handleSetFilteredData = (data: AeroFlightItem[], filters: FilterStateV2) => {
    const processedClasses = Object.keys(FLIGHT_CLASS_CONFIG).reduce((acc, classKey) => {
      const typedClassKey = classKey as keyof typeof FLIGHT_CLASS_CONFIG;
      acc[typedClassKey] = filterFlightsForClass(cloneDeep(data), typedClassKey, filters);
      return acc;
    }, {} as Record<keyof typeof FLIGHT_CLASS_CONFIG, AeroFlightItem[]>);
    const filteredFlightData: FilteredFlightData = {
      Economy: calculateFlightClassData(processedClasses.Economy, 'Economy', filters),
      Business: calculateFlightClassData(processedClasses.Business, 'Business', filters),
      Premium: calculateFlightClassData(processedClasses.Premium, 'Premium', filters),
      First: calculateFlightClassData(processedClasses.First, 'First', filters),
    };
    setFilteredFlightData(filteredFlightData);
  };

  const resetFilteredData = () => {
    setFilteredFlightData(null);
    setSearchResult([]);
    setSources([]);
    setAirlines([]);
  };

  return {
    // State
    searchResult,
    filteredFlightData,
    sources,
    airlines,
    maxFilterMileageCost,
    isLoadingFilteredData,
    isLoadingFlightData,

    // Functions
    fetchFlightData,
    handleFilterResult,
    resetFilteredData,
    handleUpdateMaxFilterMileageCost,
  };
};
