import { useState, useCallback } from 'react';
import { message } from 'antd';
import { 
  saveSearchHistory, 
  getSearchHistory, 
  getLatestSearchForRoute,
  SearchHistoryData 
} from '@/server/actions/searchHistory';

export interface SearchHistoryItem {
  id: string;
  originAirport: string;
  destinationAirport: string;
  airlines: string[];
  source: string[];
  createdAt: Date;
  updatedAt: Date;
}

export const useSearchHistory = () => {
  const [searchHistories, setSearchHistories] = useState<SearchHistoryItem[]>([]);
  const [loading, setLoading] = useState(false);

  // Save a new search to history
  const saveHistory = useCallback(async (searchData: SearchHistoryData) => {
    try {
      await saveSearchHistory(searchData);
      // Optionally refresh the history list
      await fetchSearchHistory();
    } catch (error) {
      console.error('Failed to save search history:', error);
      message.error('Failed to save search history');
    }
  }, []);

  // Fetch all search history for the user
  const fetchSearchHistory = useCallback(async (limit: number = 10) => {
    try {
      setLoading(true);
      const histories = await getSearchHistory(limit);
      setSearchHistories(histories);
      return histories;
    } catch (error) {
      console.error('Failed to fetch search history:', error);
      message.error('Failed to fetch search history');
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Get the latest search for a specific route
  const getLatestForRoute = useCallback(async (originAirport: string, destinationAirport: string) => {
    try {
      if (!originAirport || !destinationAirport) {
        return null;
      }
      
      const latestSearch = await getLatestSearchForRoute(originAirport, destinationAirport);
      return latestSearch;
    } catch (error) {
      console.error('Failed to get latest search for route:', error);
      return null;
    }
  }, []);

  return {
    searchHistories,
    loading,
    saveHistory,
    fetchSearchHistory,
    getLatestForRoute,
  };
};
