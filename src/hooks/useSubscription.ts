import { useState, useEffect, useCallback } from 'react';
import { getSubscription, consumeSearch } from '@/server/actions/subscription';
import { SubscriptionPlan } from '@/enums/subscription-plan.enum';

interface SubscriptionData {
  subscriptionPlan: SubscriptionPlan;
  searchLimit: number;
  searchesUsed: number;
  remainingSearches: number;
  lastResetDate: Date;
}

interface UseSubscriptionReturn {
  subscriptionData: SubscriptionData | null;
  loading: boolean;
  error: string | null;
  refreshSubscription: () => Promise<void>;
  consumeSubscription: () => Promise<boolean>;
}

export const useSubscription = (): UseSubscriptionReturn => {
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSubscription = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await getSubscription();
      setSubscriptionData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching subscription:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshSubscription = useCallback(async () => {
    await fetchSubscription();
  }, [fetchSubscription]);

  const consumeSubscription = useCallback(async (): Promise<boolean> => {
    try {
      setError(null);
      
      const result = await consumeSearch();
      if (result.success && result.subscriptionData) {
        setSubscriptionData(result.subscriptionData);
        return true;
      } else {
        setError(result.error || 'Failed to consume search');
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      console.error('Error consuming subscription:', err);
      return false;
    }
  }, []);

  useEffect(() => {
    fetchSubscription();
  }, [fetchSubscription]);

  return {
    subscriptionData,
    loading,
    error,
    refreshSubscription,
    consumeSubscription,
  };
};