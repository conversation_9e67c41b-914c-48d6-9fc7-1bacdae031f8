'use client';

import React, { useState } from 'react';
import { Modal, Input, Select, Switch, Button } from 'antd';
import { CheckCircleOutlined, CloseOutlined, DownOutlined } from '@ant-design/icons';
import { cn } from '@/lib/utils';

const { Option } = Select;

export interface ExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (data: ExportData) => void;
}

export interface ExportData {
  amount: string;
  currency: 'USD' | 'Points';
  displayFiatMoney: boolean;
}

const ExportModal: React.FC<ExportModalProps> = ({ isOpen, onClose, onExport }) => {
  const [amount, setAmount] = useState('');
  const [currency, setCurrency] = useState<'USD' | 'Points'>('USD');
  const [displayFiatMoney, setDisplayFiatMoney] = useState(true);

  const handleExport = () => {
    if (!amount.trim()) return;

    onExport({
      amount: amount.trim(),
      currency,
      displayFiatMoney,
    });

    // Reset form
    setAmount('');
    setCurrency('USD');
    setDisplayFiatMoney(true);
    onClose();
  };

  const handleCancel = () => {
    // Reset form
    setAmount('');
    setCurrency('USD');
    setDisplayFiatMoney(true);
    onClose();
  };

  const handleDisplayFiatMoneyChange = (value: boolean) => {
    setDisplayFiatMoney(value);
    if (value) {
      setCurrency('USD');
    } else {
      setCurrency('Points');
    }
  };

  const isExportDisabled = !amount.trim();

  return (
    <Modal
      title={null}
      open={isOpen}
      onCancel={onClose}
      footer={null}
      centered
      width={400}
      closeIcon={<CloseOutlined className="text-gray-400 text-lg hover:text-gray-600" />}
      styles={{
        content: {
          borderRadius: '16px',
          overflow: 'hidden',
          position: 'relative',
          padding: 0,
        },
        header: {
          display: 'none',
        },
      }}
      className="export-modal"
    >
      {/* Background Pattern - Decorative Grid */}
      <div className="absolute inset-0 pointer-events-none opacity-[0.02] z-0">
        <div className="absolute inset-0 bg-gradient-radial from-transparent to-black/20" />
        <svg className="absolute inset-0 w-full h-full" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="grid" width="24" height="24" patternUnits="userSpaceOnUse">
              <path d="M 24 0 L 0 0 0 24" fill="none" stroke="#E9EAEB" strokeWidth="1" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
      </div>

      {/* Modal Content */}
      <div className="relative z-10 p-6">
        {/* Modal Header */}
        <div className="text-left space-y-4 mb-6">
          {/* Success Icon */}
          <div className="flex justify-left">
            <div className="w-12 h-12 bg-green-100 border-8 border-green-50 rounded-full flex items-center justify-center">
              <CheckCircleOutlined className="text-green-600 text-2xl" />
            </div>
          </div>

          {/* Title and Description */}
          <div className="space-y-2">
            <h2 className="text-base font-semibold text-gray-900 mb-2">Exporting Data</h2>
            <p className="text-sm text-gray-600">Do you want to exporting data?</p>
          </div>
        </div>

        {/* Form Content */}
        <div className="space-y-4 mb-8">
          {/* Amount Input with Currency Selector - Matching Figma Design */}
          <div className="relative">
            <div className="flex">
              <Input
                placeholder="Enter amount"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="flex-1 h-10 text-base placeholder:text-gray-500"
                style={{
                  borderColor: '#d5d7da',
                  boxShadow: '0px 1px 2px 0px rgba(10, 13, 18, 0.05)',
                }}
                suffix={currency}
              />
            </div>
          </div>

          {/* Toggle Switch - Matching Figma Design */}
          <div className="flex items-center gap-3">
            <Switch
              checked={displayFiatMoney}
              onChange={handleDisplayFiatMoneyChange}
              className="bg-blue-600"
              style={{
                backgroundColor: displayFiatMoney ? '#1677ff' : 'rgba(0,0,0,0.25)',
              }}
            />
            <span className="text-base font-medium text-gray-700">Display fiat money</span>
          </div>
        </div>

        {/* Modal Actions */}
        <div className="flex gap-3">
          <Button
            onClick={handleCancel}
            className="flex-1 h-11 text-base font-semibold rounded-lg border-gray-300 text-gray-700 hover:bg-gray-50"
            style={{
              borderColor: '#d5d7da',
              color: '#414651',
              boxShadow:
                '0px 1px 2px -1px rgba(10, 13, 18, 0.1), 0px 1px 3px 0px rgba(10, 13, 18, 0.1), inset 0px -2px 0px 0px rgba(10, 13, 18, 0.05), inset 0px 0px 0px 1px rgba(10, 13, 18, 0.18)',
            }}
          >
            Cancel
          </Button>
          <Button
            type="primary"
            onClick={handleExport}
            disabled={isExportDisabled}
            className={cn(
              'flex-1 h-11 text-base font-semibold rounded-lg',
              isExportDisabled
                ? 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed hover:bg-gray-100'
                : 'bg-blue-600 hover:bg-blue-700 border-blue-600'
            )}
            style={{
              backgroundColor: isExportDisabled ? '#f5f5f5' : '#1677ff',
              borderColor: isExportDisabled ? '#e9eaeb' : '#1677ff',
              color: isExportDisabled ? '#a4a7ae' : '#ffffff',
              boxShadow: isExportDisabled ? 'none' : '0px 1px 2px 0px rgba(10, 13, 18, 0.05)',
            }}
          >
            Export
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ExportModal;
