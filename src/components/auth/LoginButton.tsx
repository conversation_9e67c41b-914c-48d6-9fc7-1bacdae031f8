'use client';

import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { signIn } from "next-auth/react";

interface LoginButtonProps {
  email: string;
  onLoginStart: () => void;
  onLoginEnd: () => void;
  onLoginError: (error: string) => void;
}

export function LoginButton({ email, onLoginStart, onLoginEnd, onLoginError }: LoginButtonProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async () => {
    if (!email) return;
    
    setIsLoading(true);
    onLoginStart();

    try {
      const result = await signIn("email", { 
        email, 
        callbackUrl: "/",
        redirect: false // Don't redirect automatically so we can handle errors
      });
      console.log(result);
      if (result?.error) {
        // Handle specific authentication errors
        onLoginError('This email is not authorized to access the system. Please contact your administrator.');
      } else if (result?.ok) {
        // Sign in was successful, redirect manually
        router.push('/api/auth/verify-request?provider=email&type=email');
      }
    } catch (error) {
      onLoginError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
      onLoginEnd();
    }
  };

  return (
    <Button 
      onClick={handleLogin} 
      disabled={isLoading || !email}
      className="w-full flex justify-center items-center gap-1 px-[14px] py-[10px] bg-[#155EEF] hover:bg-[##155EEF] border-0 rounded-lg shadow-sm text-sm font-semibold text-white disabled:opacity-50 disabled:cursor-not-allowed"
    >
      {isLoading ? 'Signing In...' : 'Continue'}
    </Button>
  );
}


