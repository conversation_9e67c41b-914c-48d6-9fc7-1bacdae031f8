import React, { useState, useEffect } from 'react';
import { Popover, List, Button, Empty, Typography, Spin } from 'antd';
import { ClockCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import { useSearchHistory, SearchHistoryItem } from '@/hooks/useSearchHistory';
import { FilterState } from '@/types/filters.types';

const { Text } = Typography;

interface SearchHistoryPopoverProps {
  children: React.ReactNode;
  onHistorySelect: (historyItem: SearchHistoryItem) => void;
  filterState: FilterState;
}

export const SearchHistoryPopover: React.FC<SearchHistoryPopoverProps> = ({
  children,
  onHistorySelect,
  filterState,
}) => {
  const [visible, setVisible] = useState(false);
  const { searchHistories, loading, fetchSearchHistory } = useSearchHistory();

  useEffect(() => {
    if (visible) {
      fetchSearchHistory(5); // Load last 5 searches
    }
  }, [visible, fetchSearchHistory]);

  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    return d.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const content = (
    <div style={{ width: 300, maxHeight: 400, overflow: 'auto' }}>
      <div style={{ padding: '8px 16px', borderBottom: '1px solid #f0f0f0', fontWeight: 'bold' }}>
        Recent Searches
      </div>
      {loading ? (
        <div style={{ padding: 20, textAlign: 'center' }}>
          <Spin size="small" />
        </div>
      ) : searchHistories.length === 0 ? (
        <Empty 
          image={Empty.PRESENTED_IMAGE_SIMPLE} 
          description="No search history" 
          style={{ padding: 20 }} 
        />
      ) : (
        <List
          size="small"
          dataSource={searchHistories}
          renderItem={(item) => (
            <List.Item
              actions={[
                <Button
                  key="select"
                  type="link"
                  size="small"
                  onClick={() => {
                    onHistorySelect(item);
                    setVisible(false);
                  }}
                >
                  Use
                </Button>,
              ]}
              style={{ 
                cursor: 'pointer',
                backgroundColor: 
                  item.originAirport === filterState.originAirport && 
                  item.destinationAirport === filterState.destinationAirport 
                    ? '#f6f6f6' 
                    : 'transparent'
              }}
            >
              <List.Item.Meta
                avatar={<ClockCircleOutlined style={{ color: '#1890ff' }} />}
                title={
                  <Text strong>
                    {item.originAirport} → {item.destinationAirport}
                  </Text>
                }
                description={
                  <div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      Airlines: {item.airlines.join(', ') || 'None'}
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      Programs: {item.source.join(', ') || 'None'}
                    </div>
                    <div style={{ fontSize: '11px', color: '#999' }}>
                      {formatDate(item.updatedAt)}
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      )}
    </div>
  );

  return (
    <Popover
      content={content}
      title={null}
      trigger="click"
      open={visible}
      onOpenChange={setVisible}
      placement="bottomLeft"
    >
      {children}
    </Popover>
  );
};
