import React from 'react';
import { But<PERSON>, Select } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';

import { FlightClass } from '@/enums/flight-class.enum';
import { EXPORT_CLASS_OPTIONS } from '@/constants/flight-class.constant';

interface ExportSectionProps {
  onClassChange: (value: FlightClass) => void;
  onExport: () => void;
}

export const ExportSection: React.FC<ExportSectionProps> = ({
  onClassChange,
  onExport,
}) => {
  return (
    <div className="flex gap-2 w-full justify-end items-center">
      <div>Export Screenshot</div>
      <Select
        defaultValue={FlightClass.Economy}
        style={{ width: 120 }}
        onChange={onClassChange}
        options={EXPORT_CLASS_OPTIONS.map(option => ({
          value: option.value as FlightClass,
          label: option.label,
        }))}
      />
      <Button
        className="bg-[#0d6efd]"
        type="primary"
        icon={<DownloadOutlined />}
        onClick={onExport}
      />
    </div>
  );
};
