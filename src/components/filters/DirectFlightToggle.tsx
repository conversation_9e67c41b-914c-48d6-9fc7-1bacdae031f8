import React from 'react';

interface DirectFlightToggleProps {
  isDirect: boolean;
  onChange: () => void;
}

export const DirectFlightToggle: React.FC<DirectFlightToggleProps> = ({ isDirect, onChange }) => {
  return (
    <label className="autoSaverSwitch relative inline-flex cursor-pointer select-none items-center">
      <input
        type="checkbox"
        name="autoSaver"
        className="sr-only"
        checked={isDirect}
        onChange={onChange}
      />
      <span
        className={`slider mr-3 flex h-[26px] w-[50px] items-center rounded-full p-1 duration-200 ${
          isDirect ? 'bg-[#0d6efd]' : 'bg-[#CCCCCE]'
        }`}
      >
        <span
          className={`dot h-[18px] w-[18px] rounded-full bg-white duration-200 ${
            isDirect ? 'translate-x-6' : ''
          }`}
        />
      </span>
      <span className="label flex items-center text-sm font-medium text-black">
        Direct flights Only
      </span>
    </label>
  );
};
