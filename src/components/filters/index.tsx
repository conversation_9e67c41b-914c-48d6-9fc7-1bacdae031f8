'use client';

import React, { useEffect, useState } from 'react';
import { Col, Row, message } from 'antd';

// Components
import { FinalFlightsresponse } from '@/components/flightsresults';
import { SearchForm } from './SearchForm';
import { DirectFlightToggle } from './DirectFlightToggle';
import { PointsFilter } from './PointsFilter';
import { ExportSection } from './ExportSection';

// Hooks
import { useSubscription } from '@/hooks/useSubscription';
import { useFlightSearch } from '@/hooks/useFlightSearch';
import { useSearchHistory } from '@/hooks/useSearchHistory';

// Types and Enums
import { FlightClass } from '@/enums/flight-class.enum';
import { FiltersProps, FilterState, ExportQuery } from '@/types/filters.types';
import { SubscriptionPlan } from '@/enums/subscription-plan.enum';

const Filters: React.FC<FiltersProps> = ({ origins, destinations }) => {
  // Subscription hook
  const { subscriptionData, loading: subscriptionLoading, consumeSubscription } = useSubscription();

  // Flight search hook
  const {
    searchResult,
    searchResultRender,
    sources,
    airlines,
    router,
    loading,
    isShowFilter,
    fetchFlightData,
    handleFilterResult,
    calculateMaxMileageCost,
    resetRenderData,
  } = useFlightSearch();

  // Search history hook
  const { saveHistory } = useSearchHistory();

  // Handle history selection
  const handleHistorySelect = (historyItem: any) => {
    resetRenderData();
    updateFilterState({
      originAirport: historyItem.originAirport,
      destinationAirport: historyItem.destinationAirport,
      selectedAirlines: historyItem.airlines,
      selectedSource: historyItem.source,
    });
    message.success('Search history loaded successfully');
  };

  // Filter state
  const [filterState, setFilterState] = useState<FilterState>({
    originAirport: '',
    destinationAirport: '',
    selectedAirlines: [],
    selectedSource: [],
    exportClass: FlightClass.Economy,
    isDirect: false,
    filterPoints: undefined,
    maxFilterMileageCost: undefined,
  });

  // Points filter popover state
  const [pointsPopoverOpen, setPointsPopoverOpen] = useState(false);

  // Helper function to update filter state
  const updateFilterState = (updates: Partial<FilterState>) => {
    setFilterState((prev) => ({ ...prev, ...updates }));
  };

  // Event handlers
  const handleDirectFlightToggle = () => {
    updateFilterState({ isDirect: !filterState.isDirect });
  };

  const handlePointsPopoverChange = (open: boolean) => {
    setPointsPopoverOpen(open);
  };

  // Points filter handlers
  const handlePointsChange = (value: number) => {
    updateFilterState({ filterPoints: value });
    handleFilterResult(filterState);
  };

  const handlePointsReset = () => {
    updateFilterState({ filterPoints: filterState.maxFilterMileageCost });
    handleFilterResult(filterState);
  };

  // Search handler
  const handleSearch = async () => {
    // Check subscription limits
    if (
      subscriptionData?.subscriptionPlan !== SubscriptionPlan.Unlimited &&
      subscriptionData?.remainingSearches === 0
    ) {
      message.error('You have reached your monthly search limit. Please upgrade to continue.');
      return;
    }

    // Save search to history
    try {
      await saveHistory({
        originAirport: filterState.originAirport,
        destinationAirport: filterState.destinationAirport,
        airlines: filterState.selectedAirlines,
        source: filterState.selectedSource,
      });
    } catch (error) {
      console.error('Failed to save search history:', error);
      // Don't block the search if history saving fails
    }

    handleFilterResult(filterState);
    // Refresh subscription data to get updated counts
    await consumeSubscription();
  };

  // Export handler
  const handleExport = () => {
    const query: ExportQuery = {
      selectedSource: filterState.selectedSource,
      destinationAirports: filterState.originAirport,
      selectedAirlines: filterState.selectedAirlines,
      router: router,
      isDirect: filterState.isDirect,
      points: filterState.originAirport,
      typeAir: filterState.exportClass,
    };

    localStorage.setItem('searchQuery', JSON.stringify(query));
    localStorage.setItem('searchResults', JSON.stringify(searchResultRender));
    window.location.href = '/screenshot';
  };

  // Effects
  useEffect(() => {
    fetchFlightData(filterState.originAirport, filterState.destinationAirport);
  }, [filterState.originAirport, filterState.destinationAirport, fetchFlightData]);


  useEffect(() => {
    const maxCost = calculateMaxMileageCost(searchResult, filterState);
    updateFilterState({ maxFilterMileageCost: maxCost });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    filterState.selectedAirlines,
    filterState.selectedSource,
    filterState.isDirect,
    searchResult,
    calculateMaxMileageCost,
  ]);

  // Filter destinations to exclude selected origin
  const filteredDestinations = filterState.originAirport
    ? destinations.filter((destination) => destination !== filterState.originAirport)
    : destinations;

  // Validation helper
  const isSearchDisabled = () => {
    // Block when subscription limit reached for standard plans
    if (
      subscriptionData?.subscriptionPlan !== SubscriptionPlan.Unlimited &&
      subscriptionData?.remainingSearches === 0
    ) {
      return true;
    }

    return !(
      filterState.originAirport.length > 0 &&
      filterState.destinationAirport.length > 0 &&
      filterState.selectedAirlines.length > 0 &&
      filterState.selectedSource.length > 0
    );
  };

  // Subscription info component
  const subscriptionInfo = (
    <div className="text-sm font-medium text-red-600 whitespace-nowrap">
      {subscriptionLoading ? (
        'Loading...'
      ) : subscriptionData?.subscriptionPlan === SubscriptionPlan.Unlimited ? (
        <span className="text-green-600">Unlimited</span>
      ) : (
        `API calls remaining: ${subscriptionData?.remainingSearches || 0}`
      )}
    </div>
  );

  return (
    <section className="px-4 container mx-auto py-16">
      <SearchForm
        origins={origins}
        destinations={filteredDestinations}
        airlines={airlines}
        sources={sources}
        originAirport={filterState.originAirport}
        destinationAirport={filterState.destinationAirport}
        selectedAirlines={filterState.selectedAirlines}
        selectedSource={filterState.selectedSource}
        onOriginChange={(value) => {
          // Clear destination if it's the same as the new origin
          const updates: Partial<FilterState> = { originAirport: value };
          if (filterState.destinationAirport === value) {
            updates.destinationAirport = '';
          }
          updateFilterState(updates);
        }}
        onDestinationChange={(value) => updateFilterState({ destinationAirport: value })}
        onAirlinesChange={(value) => updateFilterState({ selectedAirlines: value })}
        onSourceChange={(value) => updateFilterState({ selectedSource: value })}
        onSearch={handleSearch}
        isSearchDisabled={isSearchDisabled()}
        subscriptionInfo={subscriptionInfo}
        onHistorySelect={handleHistorySelect}
      />
      {isShowFilter && (
        <Row gutter={16} style={{ marginTop: '34px' }}>
          <Col className="gutter-row" xs={{ span: 12 }} md={{ span: 6 }}>
            <DirectFlightToggle isDirect={filterState.isDirect} onChange={handleDirectFlightToggle} />
          </Col>
          <Col className="gutter-row text-center" xs={{ span: 12 }} md={{ span: 6 }}>
            <PointsFilter
              filterPoints={filterState.filterPoints}
              maxFilterMileageCost={filterState.maxFilterMileageCost}
              open={pointsPopoverOpen}
              onOpenChange={handlePointsPopoverChange}
              onPointsChange={handlePointsChange}
              onReset={handlePointsReset}
            />
          </Col>
          <Col className="gutter-row text-right" xs={{ span: 12 }} md={{ span: 12 }}>
            <ExportSection
              onClassChange={(value) => updateFilterState({ exportClass: value })}
              onExport={handleExport}
            />
          </Col>
        </Row>
      )}
      <FinalFlightsresponse
        searchResult={searchResultRender}
        selectedSource={filterState.selectedSource}
        selectedAirlines={filterState.selectedAirlines}
        router={router}
        isRender={loading}
        isDirect={filterState.isDirect}
      />
    </section>
  );
};

export default Filters;
