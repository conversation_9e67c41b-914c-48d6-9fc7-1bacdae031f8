import React from 'react';
import { But<PERSON>, Popover, Slider } from 'antd';

interface PointsFilterProps {
  filterPoints?: number;
  maxFilterMileageCost?: number;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onPointsChange: (value: number) => void;
  onReset: () => void;
}

export const PointsFilter: React.FC<PointsFilterProps> = ({
  filterPoints,
  maxFilterMileageCost,
  open,
  onOpenChange,
  onPointsChange,
  onReset,
}) => {
  const currentValue = filterPoints || maxFilterMileageCost || 0;

  return (
    <Popover
      placement="bottom"
      content={
        <>
          <Slider
            value={currentValue}
            defaultValue={maxFilterMileageCost}
            onChange={onPointsChange}
            min={0}
            max={maxFilterMileageCost || 100}
          />
          <div className="">Max {currentValue} points</div>
          <Button className="bg-[#CCCCCE]" onClick={onReset}>
            Reset
          </Button>
        </>
      }
      trigger="click"
      open={open}
      onOpenChange={onOpenChange}
      overlayStyle={{ width: '200px' }}
    >
      <Button type="primary" className="bg-[#0d6efd]">
        Points <span style={{ marginLeft: '10px' }}>{currentValue}</span>
      </Button>
    </Popover>
  );
};
