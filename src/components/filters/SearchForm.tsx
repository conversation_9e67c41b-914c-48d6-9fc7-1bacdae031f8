import React from 'react';
import { Select, Button } from 'antd';
import { ClockCircleOutlined } from '@ant-design/icons';
import Link from 'next/link';
import { TbArrowsLeftRight } from 'react-icons/tb';
import { SearchHistoryPopover } from './SearchHistoryPopover';
import { SearchHistoryItem } from '@/hooks/useSearchHistory';
import { FilterState } from '@/types/filters.types';

const { Option } = Select;

interface SearchFormProps {
  origins: string[];
  destinations: string[];
  airlines: string[];
  sources: string[];
  originAirport: string;
  destinationAirport: string;
  selectedAirlines: string[];
  selectedSource: string[];
  onOriginChange: (value: string) => void;
  onDestinationChange: (value: string) => void;
  onAirlinesChange: (value: string[]) => void;
  onSourceChange: (value: string[]) => void;
  onSearch: () => void;
  isSearchDisabled: boolean;
  subscriptionInfo: React.ReactNode;
  onHistorySelect?: (historyItem: SearchHistoryItem) => void;
}

export const SearchForm: React.FC<SearchFormProps> = ({
  origins,
  destinations,
  airlines,
  sources,
  originAirport,
  destinationAirport,
  selectedAirlines,
  selectedSource,
  onOriginChange,
  onDestinationChange,
  onAirlinesChange,
  onSourceChange,
  onSearch,
  isSearchDisabled,
  subscriptionInfo,
  onHistorySelect,
}) => {
  return (
    <div className="md:grid md:grid-cols-4 gap-4 justify-center items-center border rounded-md py-8 px-16">
      {/* Origin Airport */}
      <label className="text-sm font-normal">
        <span className="flex items-center text-black">
          Origin Airports
          <Link className="pl-2 text-[#0d6efd] text-2xl" href={'/'}>
            <TbArrowsLeftRight />
          </Link>
        </span>
        {origins && (
          <Select
            showSearch
            onChange={onOriginChange}
            className="w-full -10 bg-white rounded-md text-black"
            value={originAirport}
          >
            {origins.map((item, index) => (
              <Option key={index} value={item}>
                {item}
              </Option>
            ))}
          </Select>
        )}
      </label>

      {/* Destination Airport */}
      <label className="text-sm font-normal">
        <span className="flex items-center text-black">
          Destination Airports
          <Link className="pl-2 text-[#0d6efd] text-2xl" href={'/'}>
            <TbArrowsLeftRight />
          </Link>
        </span>
        {destinations && (
          <Select
            showSearch
            onChange={onDestinationChange}
            className="w-full -10 bg-white rounded-md text-black"
            value={destinationAirport}
          >
            {destinations.map((item, index) => (
              <Option key={index} value={item}>
                {item}
              </Option>
            ))}
          </Select>
        )}
      </label>

      {/* Airlines */}
      <label className="text-sm font-normal">
        <span className="flex items-center text-black">
          Airline
          <Link className="pl-2 text-[#0d6efd] text-2xl" href={'/'}>
            <TbArrowsLeftRight />
          </Link>
        </span>
        {airlines && (
          <Select
            mode="multiple"
            onChange={onAirlinesChange}
            className="w-full -10 bg-white rounded-md text-black"
            value={selectedAirlines}
          >
            {airlines.map((item, index) => (
              <Option key={index} value={item}>
                {item}
              </Option>
            ))}
          </Select>
        )}
      </label>

      {/* Frequent Flyer Program */}
      <div className="block">
        <label className="text-sm font-normal">
          <span className="flex items-center text-black">
            Frequent Flyer Program
            <Link className="pl-2 text-[#0d6efd] text-2xl" href={'/'}>
              <TbArrowsLeftRight />
            </Link>
          </span>
          {sources && (
            <Select
              mode="multiple"
              value={selectedSource}
              onChange={onSourceChange}
              className="w-full -10 bg-white rounded-md text-black"
            >
              {sources.map((item, index) => (
                <Option key={index} value={item}>
                  {item}
                </Option>
              ))}
            </Select>
          )}
        </label>
      </div>

      {/* Search Button and Subscription Info */}
      <div className="items-center mt-4 flex w-full gap-3">
        <button
          onClick={onSearch}
          disabled={isSearchDisabled}
          className="bg-blue-400 text-white py-2 px-3 rounded-md hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Search
        </button>
        {onHistorySelect && (
          <SearchHistoryPopover
            onHistorySelect={onHistorySelect}
            filterState={{
              originAirport,
              destinationAirport,
              selectedAirlines,
              selectedSource,
            } as FilterState}
          >
            <Button 
              icon={<ClockCircleOutlined />}
              type="default"
              size="middle"
              title="Search History"
            >
              History
            </Button>
          </SearchHistoryPopover>
        )}
        <div className="flex-1" />
        {subscriptionInfo}
      </div>
    </div>
  );
};
