import React from 'react';
import { cn } from '@/lib/utils';

interface FeaturedIconProps {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

const sizeVariants = {
  sm: 'w-10 h-10',
  md: 'w-12 h-12', 
  lg: 'w-14 h-14',
  xl: 'w-14 h-14'
};

const iconSizeVariants = {
  sm: 'w-5 h-5',
  md: 'w-6 h-6',
  lg: 'w-7 h-7', 
  xl: 'w-9 h-9'
};

export const FeaturedIcon: React.FC<FeaturedIconProps> = ({ 
  children, 
  className = '', 
  size = 'xl' 
}) => {
  return (
    <div className={cn(
      'relative rounded-full overflow-hidden',
      sizeVariants[size],
      className
    )}>
      {/* Gradient background with mask */}
      <div className="absolute inset-0 bg-gradient-to-b from-black to-transparent opacity-100" />
      
      {/* Background color circle */}
      <div className="absolute inset-0 bg-[#EFF4FF] border border-[#B2CCFF] rounded-full" />
      
      {/* Inner icon container */}
      <div className={cn(
        'absolute inset-2 bg-[#155EEF] rounded-full flex items-center justify-center',
        size === 'xl' ? 'inset-2.5' : 'inset-2'
      )}>
        <div className={cn('text-white flex items-center justify-center', iconSizeVariants[size])}>
          {children}
        </div>
      </div>
    </div>
  );
};
