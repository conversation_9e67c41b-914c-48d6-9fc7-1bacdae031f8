import * as React from "react";
import { cn } from "@/lib/utils";

type BadgeVariant = "default" | "warning" | "purple";
type BadgeSize = "sm" | "md" | "lg";
type BadgeShape = "default" | "pill";

const getVariantClasses = (variant: BadgeVariant): string => {
  switch (variant) {
    case "warning":
      return "border-orange-200 bg-orange-50 text-orange-700";
    case "purple":
      return "border-purple-200 bg-purple-50 text-purple-700";
    default:
      return "border-gray-300 bg-white text-gray-700 shadow-xs";
  }
};

const getSizeClasses = (size: BadgeSize): string => {
  switch (size) {
    case "sm":
      return "px-2 py-0.5 text-xs";
    case "lg":
      return "px-3 py-1 text-base";
    default:
      return "px-2.5 py-0.5 text-sm";
  }
};

const getShapeClasses = (shape: BadgeShape): string => {
  switch (shape) {
    case "pill":
      return "rounded-full";
    default:
      return "rounded-md";
  }
};

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: BadgeVariant;
  size?: BadgeSize;
  shape?: BadgeShape;
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant = "default", size = "md", shape = "default", ...props }, ref) => {
    const baseClasses = "inline-flex items-center justify-center border font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2";
    const variantClasses = getVariantClasses(variant);
    const sizeClasses = getSizeClasses(size);
    const shapeClasses = getShapeClasses(shape);
    
    return (
      <div
        ref={ref}
        className={cn(baseClasses, variantClasses, sizeClasses, shapeClasses, className)}
        {...props}
      />
    );
  }
);
Badge.displayName = "Badge";

export { Badge };
