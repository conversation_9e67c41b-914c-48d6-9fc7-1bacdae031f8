'use client';

import React, { useState } from 'react';
import { Button } from 'antd';
import ExportModal, { ExportData } from '@/components/modals/ExportModal';

const ExportModalExample: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleExport = (data: ExportData) => {
    console.log('Export data:', data);
    // Here you would typically handle the export logic
    alert(`Exporting ${data.amount} ${data.currency} with fiat display: ${data.displayFiatMoney}`);
  };

  return (
    <div className="p-8 min-h-screen bg-gray-50">
      <div className="max-w-md mx-auto space-y-4">
        <h1 className="text-2xl font-bold text-center text-gray-900">Export Modal Example</h1>
        <p className="text-gray-600 text-center">
          Click the button below to open the export modal that matches the Figma design.
        </p>
        
        <div className="flex justify-center">
          <Button 
            type="primary" 
            onClick={handleOpenModal}
            className="h-10 px-6"
            style={{ backgroundColor: '#1677ff' }}
          >
            Open Export Modal
          </Button>
        </div>

        <ExportModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          onExport={handleExport}
        />
      </div>
    </div>
  );
};

export default ExportModalExample;