import { useState, useEffect } from 'react';

export const FinalFlightsresponse: React.FC<any> = ({
  searchResult,
  selectedSource,
  selectedAirlines,
  router,
  isRender,
  isDirect,
}) => {
  const [loading, setLoading] = useState<boolean>(isRender);

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(!isRender);
    }, 1000);

    return () => clearTimeout(timer);
  }, [isRender]);
  if (loading && isRender) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div
          className="text-center inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-e-transparent align-[-0.125em] text-surface motion-reduce:animate-[spin_1.5s_linear_infinite] dark:text-red-800"
          role="status"
        >
          <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
            Loading...
          </span>
        </div>
      </div>
    );
  }

  if (isRender) {
    return null;
  }
  // console.info("searchResult: ", searchResult);
  return (
    <>
      <section>
        <h1 className="text-center py-6 font-bold">Search Results</h1>
        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6 md:gap-8 items-start px-2 md:px-0">
          {Object.keys(searchResult).map((typeAir: any, index: number) => {
            const airlineSet = new Set<string>();
            const mileageCost = new Set<string>();
            const programSource = new Set<string>();
            const totalAvailable = Object.keys(searchResult[typeAir]).length;

            Object.keys(searchResult[typeAir]).map((month: any, index: number) => {
              Object.keys(searchResult[typeAir][month]).map((day: string, dayIndex: number) => {
                searchResult[typeAir][month][day].map((item: any) => {
                  programSource.add(item.Source);
                  if (typeAir === 'Economy') {
                    item.YAirlines?.split(',').forEach((itm: any) => itm && airlineSet.add(itm.trim()));
                    mileageCost.add(new Intl.NumberFormat().format(item.YMileageCost));
                  } else if (typeAir === 'Business') {
                    item.JAirlines?.split(',').forEach((itm: any) => itm && airlineSet.add(itm.trim()));
                    mileageCost.add(new Intl.NumberFormat().format(item.JMileageCost));
                  } else if (typeAir === 'Premium') {
                    item.WAirlines?.split(',').forEach((itm: any) => itm && airlineSet.add(itm.trim()));
                    mileageCost.add(new Intl.NumberFormat().format(item.WMileageCost));
                  } else if (typeAir === 'First') {
                    item.FAirlines?.split(',').forEach((itm: any) => itm && airlineSet.add(itm.trim()));
                    mileageCost.add(new Intl.NumberFormat().format(item.FMileageCost));
                  }
                });
              });
            });

            return (
              <div
                id={typeAir}
                className="w-full p-4 rounded-md bg-gray-900 text-white shadow-xl overflow-hidden border-gray-800 relative z-10"
                key={index}
              >
                <div className="flex space-x-2 justify-between">
                  <span className="font-bold text-red-500">{typeAir}</span>
                  <p className="font-bold text-teal-500">
                    {selectedAirlines.length > 0 ? selectedAirlines : Array.from(airlineSet).join(', ')}
                  </p>
                </div>

                <div className="p-4 bg-gray-800 border-gray-700 mt-4">
                  <div className="flex justify-between items-center">
                    <div className="text-center">
                      <span className="font-semibold block">{router.OriginAirport}</span>
                      <span className="text-sm text-gray-400">Departure</span>
                    </div>
                    <div className="w-6 h-4 text-pink-500">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        xmlnsXlink="http://www.w3.org/1999/xlink"
                        fill="#ec4899"
                        height="24"
                        width="24"
                        version="1.1"
                        id="Layer_1"
                        viewBox="0 0 330 330"
                        xmlSpace="preserve"
                        className="lucide lucide-arrow-left-right w-6 h-6 text-pink-500"
                      >
                        <path
                          id="XMLID_27_"
                          d="M15,180h263.787l-49.394,49.394c-5.858,5.857-5.858,15.355,0,21.213C232.322,253.535,236.161,255,240,255  s7.678-1.465,10.606-4.394l75-75c5.858-5.857,5.858-15.355,0-21.213l-75-75c-5.857-5.857-15.355-5.857-21.213,0  c-5.858,5.857-5.858,15.355,0,21.213L278.787,150H15c-8.284,0-15,6.716-15,15S6.716,180,15,180z"
                        />
                      </svg>
                    </div>
                    <div className="text-center">
                      <span className="font-semibold block">{router.DestinationAirport}</span>
                      <span className="text-sm text-gray-400">Arrival</span>
                    </div>
                  </div>
                </div>

                <div className="rounded-xl border text-card-foreground shadow p-4 bg-gray-800 space-y-3 border-gray-700 mt-4 text-sm">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Flight Type</span>
                    <div className="inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-pink-500 bg-opacity-20 text-pink-300 border-pink-500">
                      {isDirect ? 'Direct Flight' : 'Has connections'}
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Frequent Flyer</span>
                    <div className="inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-blue-500 bg-opacity-20 text-blue-300 border-blue-500">
                      {selectedSource.length > 0
                        ? selectedSource.join(', ')
                        : Array.from(programSource).join(', ')}
                    </div>
                  </div>
                  <div className="flex justify-between items-center gap-2">
                    <span className="text-gray-400">Points Required</span>
                    <span className="font-semibold text-pink-500 text-right">
                      {Array.from(mileageCost).length > 0
                        ? Array.from(mileageCost).join(', ')
                        : new Intl.NumberFormat().format(router.Distance)}{' '}
                      Points
                    </span>
                  </div>
                </div>

                <div className="rounded-xl border text-card-foreground shadow p-4 bg-gray-800 border-gray-700 mt-4">
                  <h3 className="font-semibold mb-2 text-gray-300">
                    {totalAvailable == 0 && 'No '}Availability
                  </h3>
                  {Object.keys(searchResult[typeAir]).map((month: any, index: number) => (
                    <div className="flex flex-wrap gap-2 mt-4" key={index}>
                      {Object.keys(searchResult[typeAir][month]).map((day: string, dayIndex: number) => {
                        const total = Object.keys(searchResult[typeAir][month]).length - 1;
                        let seatsAvailable = 0;
                        if (typeAir === 'Economy') {
                          seatsAvailable = searchResult[typeAir][month][day].reduce(
                            (n: any, { YRemainingSeats }: any) => n + YRemainingSeats,
                            0
                          );
                        } else if (typeAir === 'Business') {
                          seatsAvailable = searchResult[typeAir][month][day].reduce(
                            (n: any, { JRemainingSeats }: any) => n + JRemainingSeats,
                            0
                          );
                        } else if (typeAir === 'Premium') {
                          seatsAvailable = searchResult[typeAir][month][day].reduce(
                            (n: any, { WRemainingSeats }: any) => n + WRemainingSeats,
                            0
                          );
                        } else if (typeAir === 'First') {
                          seatsAvailable = searchResult[typeAir][month][day].reduce(
                            (n: any, { FRemainingSeats }: any) => n + FRemainingSeats,
                            0
                          );
                        }

                        let showCountSeat = seatsAvailable > 0 ? `(${seatsAvailable})` : '';
                        const shortDate = new Date(day).getDate().toString();
                        return (
                          <div
                            key={dayIndex}
                            className="inline-flex items-center rounded-md border font-semibold focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-xs px-2 py-1 bg-gray-700 text-gray-200 border-gray-600 hover:bg-gray-600 transition-colors"
                          >
                            {month.slice(0, 3)} {shortDate} {showCountSeat}
                          </div>
                        );
                      })}
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </section>
    </>
  );
};
