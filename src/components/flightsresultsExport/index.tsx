import { useState, useEffect } from 'react';
import domtoimage from 'dom-to-image';
import Image from 'next/image';
export const FinalFlightsresponseExport: React.FC<any> = ({
  searchResult,
  selectedSource,
  selectedAirlines,
  router,
  isRender,
  isDirect,
  typeFly,
}) => {
  const [loading, setLoading] = useState<boolean>(isRender);

  const captureScreenshot = () => {
    const element = document.querySelector('#' + typeFly);
    if (element) {
      setLoading(true);
      domtoimage
        .toPng(element, {
          width: 1200,
          height: 1080,
          style: {
            transform: 'scale(1)',
            transformOrigin: 'top left',
            overflow: 'visible',
            width: '1200px',
            height: '1080px',
          },
        })
        .then((dataUrl: any) => {
          const link = document.createElement('a');
          link.href = dataUrl;
          link.download = typeFly + '-screenshot-result.png';
          link.click();
          setLoading(false);
          window.location.href = '/';
        })
        .catch((err: any) => {
          console.log('Error capturing the screenshot: ' + err.message);
        });
    } else {
      console.log(`Element with selector "${typeFly}" not found.`);
    }
  };
  useEffect(() => {
    // setTimeout(() => {
    //   const responseEx = captureScreenshot();
    // }, 2000);
  }, [typeFly]);

  if (!typeFly) {
    return null;
  }
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];
  const airlineLogo = [
    'AY',
    'BR',
    'CI',
    'CX',
    'EK',
    'JL',
    'JQ',
    'NH',
    'QF',
    'QR',
    'SA',
    'SQ',
    'UA',
    'UL',
    'VA',
    'MH',
    'TG',
  ];
  const AirlineLogo = (code: string) => {
    const isCodeInArray = airlineLogo.includes(code);

    if (isCodeInArray) {
      return <Image src={`/airline/${code}.png`} alt={`${code} logo`} />;
    }

    return <span>{code}</span>;
  };

  const getDisplayText = (selectedSource: string[] | undefined): string => {
    if (!selectedSource || selectedSource.length === 0) return 'Points';

    const airlineMap: Record<string, string> = {
      qantas: 'Qantas points',
      velocity: 'Velocity points',
      singapore: 'KrisFlyer miles',
    };

    return airlineMap[selectedSource[0]] || 'Points';
  };

  return (
    <>
      {loading && (
        <div className="flex justify-center items-center h-screen w-screen fixed z-50 bg-white/50">
          <div
            className="text-center inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-e-transparent align-[-0.125em] text-surface motion-reduce:animate-[spin_1.5s_linear_infinite] dark:text-red-800"
            role="status"
          >
            <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
              Loading...
            </span>
          </div>
        </div>
      )}
      <section className="relative w-[1200px] h-[1080px] m-auto overflow-visible">
        {Object.keys(searchResult).map((typeAir: any, index: number) => {
          const airlineSet = new Set<string>();
          const mileageCost = new Set<string>();
          const programSource = new Set<string>();
          // const totalAvailable = Object.keys(searchResult[typeAir]).length;

          if (typeAir !== typeFly) {
            return;
          }
          Object.keys(searchResult[typeAir]).map((month: any, index: number) => {
            Object.keys(searchResult[typeAir][month]).map((day: string, dayIndex: number) => {
              searchResult[typeAir][month][day].map((item: any) => {
                programSource.add(item.Source);
                if (typeAir === 'Economy') {
                  item.YAirlines?.split(',').forEach((itm: any) => itm && airlineSet.add(itm.trim()));
                  mileageCost.add(new Intl.NumberFormat().format(item.YMileageCost));
                } else if (typeAir === 'Business') {
                  item.JAirlines?.split(',').forEach((itm: any) => itm && airlineSet.add(itm.trim()));
                  mileageCost.add(new Intl.NumberFormat().format(item.JMileageCost));
                } else if (typeAir === 'Premium') {
                  item.WAirlines?.split(',').forEach((itm: any) => itm && airlineSet.add(itm.trim()));
                  mileageCost.add(new Intl.NumberFormat().format(item.WMileageCost));
                } else if (typeAir === 'First') {
                  item.FAirlines?.split(',').forEach((itm: any) => itm && airlineSet.add(itm.trim()));
                  mileageCost.add(new Intl.NumberFormat().format(item.FMileageCost));
                }
              });
            });
          });
          const minValue = Math.min(
            ...Array.from(mileageCost).map((value) => parseInt(value.replace(/,/g, ''), 10))
          );

          return (
            <div
              id={typeFly}
              className="text-white shadow-xl relative z-10 bg-[url('/ysf-bg.webp')] bg-cover bg-no-repeat bg-center"
              key={index}
            >
              <div className="list-calendar-bg"></div>
              <div className="grid grid-cols-3 px-[86px] py-5 items-center mb-8">
                <div className="flex space-x-2 justify-between">
                  <p className="font-bold text-teal-500 text-[80px] max-w-sm">
                    {selectedAirlines.length > 0
                      ? AirlineLogo(selectedAirlines[0])
                      : Array.from(airlineSet).join(', ')}
                  </p>
                </div>

                <div className="flex justify-end items-center text-[#166078] gap-4">
                  <span className="font-semibold text-[38px]">{router.OriginAirport}</span>
                  <div className="w-6 h-4 text-pink-500">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      xmlnsXlink="http://www.w3.org/1999/xlink"
                      fill="#ec4899"
                      height="24"
                      width="24"
                      version="1.1"
                      id="Layer_1"
                      viewBox="0 0 330 330"
                      xmlSpace="preserve"
                      className="lucide lucide-arrow-left-right w-6 h-6 text-pink-500"
                    >
                      <path
                        id="XMLID_27_"
                        d="M15,180h263.787l-49.394,49.394c-5.858,5.857-5.858,15.355,0,21.213C232.322,253.535,236.161,255,240,255  s7.678-1.465,10.606-4.394l75-75c5.858-5.857,5.858-15.355,0-21.213l-75-75c-5.857-5.857-15.355-5.857-21.213,0  c-5.858,5.857-5.858,15.355,0,21.213L278.787,150H15c-8.284,0-15,6.716-15,15S6.716,180,15,180z"
                      />
                    </svg>
                  </div>
                  <span className="font-semibold text-[38px]">{router.DestinationAirport}</span>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-end items-center gap-3">
                    <div className="inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-pink-500 bg-opacity-20 text-pink-300 border-pink-500">
                      {isDirect ? 'Direct Flight' : 'Has connections'}
                    </div>
                    <span className="font-bold text-red-500 text-[20px]">{typeAir}</span>
                  </div>
                  {/* <div className="flex justify-end items-center gap-3">
                    <span className="text-gray-400">Frequent Flyer</span>
                    <div className="inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-blue-500 bg-opacity-20 text-blue-300 border-blue-500">{selectedSource.length > 0 ? selectedSource.join(", ") : Array.from(programSource).join(", ")}</div>
                  </div> */}
                  <div className="font-semibold text-pink-500 text-right text-[26px]">
                    {Array.from(mileageCost).length > 0
                      ? minValue
                      : new Intl.NumberFormat().format(router.Distance)}{' '}
                    - {getDisplayText(selectedSource)}
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-4 list-calendar">
                {months.map((checkmonth: any, index: number) => {
                  if (searchResult[typeAir].hasOwnProperty(checkmonth)) {
                    const firstDateKey = Object.keys(searchResult[typeAir][checkmonth])[0];
                    const year = new Date(firstDateKey).getFullYear();
                    return (
                      <div className="flex flex-col gap-1 list-calendar-col" key={index}>
                        <div className="list-calendar-month">
                          {checkmonth} - {year}
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {Object.keys(searchResult[typeAir][checkmonth]).map(
                            (day: string, dayIndex: number) => {
                              // const total = Object.keys(searchResult[typeAir][checkmonth]).length - 1;
                              let seatsAvailable = 0;
                              if (typeAir === 'Economy') {
                                seatsAvailable = searchResult[typeAir][checkmonth][day].reduce(
                                  (n: any, { YRemainingSeats }: any) => n + YRemainingSeats,
                                  0
                                );
                              } else if (typeAir === 'Business') {
                                seatsAvailable = searchResult[typeAir][checkmonth][day].reduce(
                                  (n: any, { JRemainingSeats }: any) => n + JRemainingSeats,
                                  0
                                );
                              } else if (typeAir === 'Premium') {
                                seatsAvailable = searchResult[typeAir][checkmonth][day].reduce(
                                  (n: any, { WRemainingSeats }: any) => n + WRemainingSeats,
                                  0
                                );
                              } else if (typeAir === 'First') {
                                seatsAvailable = searchResult[typeAir][checkmonth][day].reduce(
                                  (n: any, { FRemainingSeats }: any) => n + FRemainingSeats,
                                  0
                                );
                              }

                              let showCountSeat = seatsAvailable > 0 ? `(${seatsAvailable})` : '';
                              const shortDate = new Date(day).getDate().toString();
                              return (
                                <div
                                  key={dayIndex}
                                  className="inline-flex items-center rounded-md border font-semibold text-xs px-2 py-1 text-black border-gray-600 transition-colors"
                                >
                                  {shortDate} {showCountSeat}
                                </div>
                              );
                            }
                          )}
                        </div>
                      </div>
                    );
                  }
                  return (
                    <div className="flex flex-col gap-1 list-calendar-col" key={index}>
                      <div className="list-calendar-month">{checkmonth}</div>
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </section>
    </>
  );
};
