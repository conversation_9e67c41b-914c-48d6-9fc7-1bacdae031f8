{"name": "ysf", "version": "0.1.0", "private": true, "scripts": {"postinstall": "prisma generate", "dev": "next dev", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts", "db:seed:prod": "env-cmd -f .env.production tsx prisma/seed.ts", "prisma": "prisma", "prisma:prod": "env-cmd -f .env.production prisma", "vercel": "vercel"}, "dependencies": {"@neondatabase/serverless": "^1.0.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/adapter-neon": "^6.16.0", "@prisma/client": "^6.15.0", "@radix-ui/react-slot": "^1.2.3", "@types/lodash": "^4.17.0", "antd": "5.27.4", "axios": "^1.6.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "dom-to-image": "^2.6.0", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "moment": "^2.30.1", "next": "^14.2.32", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "prisma": "^6.15.0", "puppeteer": "^23.8.0", "react": "^18", "react-dom": "^18", "react-icons": "^5.0.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "ws": "^8.18.3"}, "devDependencies": {"@types/dom-to-image": "^2.6.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/ws": "^8.18.1", "autoprefixer": "^10.0.1", "bufferutil": "^4.0.9", "env-cmd": "^11.0.0", "eslint": "^8", "eslint-config-next": "14.1.2", "postcss": "^8", "tailwindcss": "^3.3.0", "tsx": "^4.19.1", "typescript": "^5", "vercel": "^47.1.4"}}