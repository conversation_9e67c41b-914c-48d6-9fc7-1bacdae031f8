-- CreateTable
CREATE TABLE "public"."SearchHistories" (
    "id" VARCHAR(36) NOT NULL,
    "userId" TEXT NOT NULL,
    "originAirport" TEXT NOT NULL,
    "destinationAirport" TEXT NOT NULL,
    "airlines" TEXT[],
    "source" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SearchHistories_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "SearchHistories_userId_originAirport_destinationAirport_idx" ON "public"."SearchHistories"("userId", "originAirport", "destinationAirport");

-- AddForeignKey
ALTER TABLE "public"."SearchHistories" ADD CONSTRAINT "SearchHistories_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
