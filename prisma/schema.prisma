generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Airports {
  id        String   @id @default(uuid()) @db.Var<PERSON>har(36)
  name      String
  iataCode  String   @unique @db.VarChar(3)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model User {
  id               String             @id @default(uuid()) @db.VarChar(36)
  name             String?
  email            String?            @unique
  emailVerified    DateTime?
  image            String?
  lastLoginAt      DateTime?
  subscriptionPlan String             @default("standard") // "standard" or "unlimited"
  searchLimit      Int                @default(500) // Monthly search limit (500 for standard, -1 for unlimited)
  searchesUsed     Int                @default(0) // Current month searches used
  lastResetDate    DateTime           @default(now()) // Last time the counter was reset
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @default(now()) @updatedAt
  accounts         Account[]
  sessions         Session[]
  searchHistories  SearchHistories[]
}

model Account {
  id                String  @id @default(uuid()) @db.VarChar(36)
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(uuid()) @db.VarChar(36)
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @default(now()) @updatedAt
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @default(now()) @updatedAt

  @@unique([identifier, token])
}

model SearchHistories {
  id                 String   @id @default(uuid()) @db.VarChar(36)
  userId             String
  originAirport      String
  destinationAirport String
  airlines           String[]
  source             String[]
  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now()) @updatedAt
  user               User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, originAirport, destinationAirport])
}
